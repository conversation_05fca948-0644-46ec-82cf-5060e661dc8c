# Design Document

## Overview

Este design implementa um sistema robusto de gerenciamento de sessões para comunicação webhook com o agente N8N. O sistema garante que cada conversa tenha um identificador único (session_id) que é persistido localmente e enviado consistentemente para o N8N, permitindo que o agente mantenha contexto e memória das conversas.

## Architecture

### Session Management Flow
```
[Extensão Plasmo] → [Session Manager] → [Webhook API] → [N8N Agent]
                                    ↓
                              [LocalStorage]
```

### Key Components
1. **SessionManager**: Gerencia criação, persistência e recuperação de session_ids
2. **Enhanced WebhookAPI**: Versão melhorada que inclui session_id em todos os payloads
3. **Session Persistence**: Armazenamento local de sessões ativas e históricas
4. **Payload Formatter**: Estrutura consistente de dados enviados ao N8N

## Components and Interfaces

### SessionManager Interface
```typescript
interface SessionManager {
  getCurrentSessionId(): string | null;
  createNewSession(userId: string): string;
  persistSession(sessionId: string, metadata: SessionMetadata): void;
  loadSession(sessionId: string): SessionData | null;
  finalizeSession(sessionId: string): void;
}

interface SessionMetadata {
  userId: string;
  createdAt: string;
  lastActivity: string;
  messageCount: number;
  status: 'active' | 'finalized';
}
```

### Enhanced Webhook Payload
```typescript
interface WebhookPayload {
  message: string;
  session_id: string;  // Sempre presente
  user_id: string;
  context: {
    timestamp: string;
    fonte: string;
    user_agent: string;
    message_sequence: number;
    session_metadata: SessionMetadata;
  };
}

interface WebhookResponse {
  response: string;
  session_id: string;  // Confirmação do N8N
  message_id: string;
  timestamp: string;
  processing_time?: number;
}
```

### Session Storage Schema
```typescript
interface StoredSession {
  sessionId: string;
  userId: string;
  createdAt: string;
  lastActivity: string;
  messageCount: number;
  status: 'active' | 'finalized';
  messages: StoredMessage[];
}

interface StoredMessage {
  id: string;
  sessionId: string;
  type: 'user' | 'assistant';
  content: string;
  timestamp: string;
  sequence: number;
}
```

## Data Models

### Session Lifecycle States
1. **Created**: Session_id gerado, não enviado ainda
2. **Active**: Session_id enviado ao N8N, conversa ativa
3. **Persisted**: Session salva no localStorage
4. **Finalized**: Session encerrada, marcada como finalizada

### Session ID Generation Strategy
```typescript
const generateSessionId = (userId: string): string => {
  const timestamp = Date.now();
  const random = Math.random().toString(36).substring(2, 8);
  return `session_${userId}_${timestamp}_${random}`;
};
```

### Storage Keys
- `webhook_active_session`: Sessão atualmente ativa
- `webhook_session_history`: Histórico de sessões
- `webhook_session_metadata`: Metadados das sessões

## Error Handling

### Session Recovery Strategies
1. **Connection Loss**: Manter session_id local até reconexão
2. **Invalid Session**: Criar nova sessão automaticamente
3. **N8N Error**: Retry com mesmo session_id
4. **Storage Error**: Fallback para session temporária

### Error Scenarios
```typescript
interface SessionError {
  type: 'connection' | 'invalid_session' | 'storage' | 'n8n_error';
  sessionId: string;
  message: string;
  timestamp: string;
  recoveryAction: 'retry' | 'new_session' | 'fallback';
}
```

## Testing Strategy

### Unit Tests
- SessionManager creation and persistence
- Session ID generation uniqueness
- Payload formatting with session data
- Error handling and recovery

### Integration Tests
- End-to-end webhook communication with session
- Session persistence across browser restarts
- Multiple concurrent sessions handling
- N8N response processing with session validation

### Manual Testing Scenarios
1. **New Conversation**: Verificar criação automática de session_id
2. **Conversation Continuation**: Verificar uso do session_id existente
3. **Browser Restart**: Verificar recuperação de sessão ativa
4. **Connection Error**: Verificar manutenção de session_id durante retry
5. **Session Finalization**: Verificar notificação ao N8N

## Implementation Considerations

### Performance Optimizations
- Lazy loading de sessões históricas
- Debounce para salvamento de metadados
- Cleanup automático de sessões antigas
- Indexação eficiente no localStorage

### Security Considerations
- Session IDs não devem conter informações sensíveis
- Validação de session_id recebido do N8N
- Sanitização de dados antes do armazenamento
- Rate limiting para criação de sessões

### Backward Compatibility
- Manter compatibilidade com API local existente
- Graceful fallback se N8N não suportar session_id
- Migração suave de sessões existentes sem session_id

### Monitoring and Logging
```typescript
interface SessionLog {
  level: 'info' | 'warn' | 'error';
  sessionId: string;
  action: string;
  details: any;
  timestamp: string;
}
```

## Configuration Options

### New Configuration Fields
```typescript
interface WebhookConfig extends ConfiguracaoUnificada {
  sessionManagement: {
    enabled: boolean;
    autoCleanupDays: number;
    maxStoredSessions: number;
    enableDetailedLogging: boolean;
  };
}
```