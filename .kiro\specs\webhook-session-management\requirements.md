# Requirements Document

## Introduction

Esta funcionalidade visa melhorar a comunicação entre a extensão Plasmo e o agente N8N, garantindo que o session_id seja corretamente enviado e gerenciado via webhook. Isso permitirá que o agente N8N armazene e gerencie sessões de chat em sua memória (banco simples), proporcionando continuidade nas conversas e melhor rastreamento de contexto.

## Requirements

### Requirement 1

**User Story:** Como usuário da extensão, eu quero que minhas conversas via webhook N8N mantenham continuidade de sessão, para que o agente possa lembrar do contexto anterior e fornecer respostas mais relevantes.

#### Acceptance Criteria

1. WHEN uma nova conversa é iniciada via webhook THEN o sistema SHALL gerar um session_id único e enviá-lo no payload
2. WHEN uma conversa existente continua via webhook THEN o sistema SHALL enviar o session_id existente no payload
3. WHEN o N8N recebe uma mensagem THEN o sistema SHALL armazenar o session_id em sua memória para referência futura
4. WHEN o usuário envia múltiplas mensagens na mesma sessão THEN o sistema SHALL manter o mesmo session_id durante toda a conversa

### Requirement 2

**User Story:** Como desenvolvedor, eu quero que o payload do webhook contenha informações estruturadas de sessão, para que o agente N8N possa processar e armazenar adequadamente os dados da conversa.

#### Acceptance Criteria

1. WHEN enviando dados via webhook THEN o sistema SHALL incluir session_id como campo obrigatório no payload
2. WHEN o session_id não existe THEN o sistema SHALL gerar um novo usando timestamp e identificador único
3. WHEN o payload é enviado THEN o sistema SHALL incluir metadados da sessão (user_id, timestamp, fonte)
4. WHEN o N8N responde THEN o sistema SHALL processar e armazenar o session_id retornado na resposta

### Requirement 3

**User Story:** Como usuário, eu quero que o gerenciamento de sessões via webhook seja transparente, para que eu não precise me preocupar com detalhes técnicos durante o uso.

#### Acceptance Criteria

1. WHEN inicio uma nova conversa THEN o sistema SHALL automaticamente criar e gerenciar o session_id
2. WHEN continuo uma conversa existente THEN o sistema SHALL automaticamente usar o session_id correto
3. WHEN há erro na comunicação THEN o sistema SHALL manter o session_id local até que a conexão seja restabelecida
4. WHEN a sessão é finalizada THEN o sistema SHALL notificar o N8N sobre o encerramento da sessão

### Requirement 4

**User Story:** Como administrador do sistema, eu quero que haja logs e rastreabilidade das sessões webhook, para que eu possa monitorar e debugar problemas de comunicação.

#### Acceptance Criteria

1. WHEN uma mensagem é enviada via webhook THEN o sistema SHALL logar o session_id e payload enviado
2. WHEN uma resposta é recebida do N8N THEN o sistema SHALL logar o session_id e dados recebidos
3. WHEN há erro na comunicação THEN o sistema SHALL logar detalhes do erro incluindo session_id
4. IF logs estão habilitados THEN o sistema SHALL exibir informações de debug no console do navegador