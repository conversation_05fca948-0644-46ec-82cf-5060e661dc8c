# Product Overview

**Fibra Epsilon** is a browser extension built for FibraLink that provides an AI-powered chatbot assistant called "<PERSON><PERSON><PERSON>" to help with streaming application integration problems.

## Core Purpose
- Assists users with technical streaming issues (Disney+, Netflix, etc.)
- Provides real-time chat support through AI assistant
- Manages conversation sessions and history
- Offers both API and webhook connectivity options

## Key Features
- **Real-time Chat**: Interactive conversations with SOGE assistant
- **Session Management**: Persistent chat sessions with history
- **Dual Connectivity**: Supports both direct API calls and N8N webhook integration
- **Configuration Management**: Customizable API endpoints and connection settings
- **History Tracking**: View and resume previous conversations
- **Markdown Support**: Rich text formatting in chat responses

## Target Users
Technical support staff and users dealing with streaming application integration issues at FibraLink.

## Business Context
This is an internal tool for FibraLink (formerly FibraEpsilon) to streamline technical support for streaming service integrations.