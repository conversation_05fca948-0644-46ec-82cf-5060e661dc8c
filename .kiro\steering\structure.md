# Project Structure

## Root Level
- **popup.tsx**: Main entry point for extension popup UI
- **globals.css**: Global CSS styles and Tailwind imports
- **package.json**: Dependencies and build scripts
- **tsconfig.json**: TypeScript configuration with path aliases
- **tailwind.config.js**: Tailwind CSS configuration with custom theme
- **components.json**: Shadcn/ui component configuration

## Source Organization (`src/`)

### Components (`src/components/`)
- **ChatbotSOGE-minimal.tsx**: Main chatbot interface component
- **ConfiguracoesChatbot.tsx**: Settings/configuration modal
- **HistoricoSessoes.tsx**: Chat history management modal

### UI Components (`src/components/ui/`)
- **Shadcn/ui components**: button, card, input, textarea, etc.
- **kibo-ui/**: Advanced UI components for AI interfaces
  - `ai/`: Conversation, message, and response components
  - `code-block/`: Code syntax highlighting components
- **simple-markdown.tsx**: Custom markdown renderer

### Business Logic (`src/hooks/`)
- **useChatbotAPI.ts**: Core API communication hook
- **useChatbotUnificado.ts**: Unified hook for dual connectivity
- **useWebhookAPI.ts**: Webhook-specific API handling

### Configuration (`src/config/`)
- **default.ts**: Default application configuration

### Utilities (`src/lib/`)
- **utils.ts**: Common utility functions and helpers

## Build Artifacts
- **.plasmo/**: Generated Plasmo framework files
- **build/**: Production build output
- **node_modules/**: Dependencies

## Documentation (`docs/`)
- **backend-response-format.md**: API response specifications
- **soge-response-examples.md**: Example API responses
- **README_FRONTEND.md**: Comprehensive frontend documentation

## Naming Conventions
- **Components**: PascalCase (e.g., `ChatbotSOGE.tsx`)
- **Hooks**: camelCase with `use` prefix (e.g., `useChatbotAPI.ts`)
- **Types/Interfaces**: PascalCase (e.g., `ConfiguracaoAPI`)
- **Constants**: UPPER_SNAKE_CASE (e.g., `SESSAO_ATIVA_KEY`)
- **Files**: kebab-case for utilities, PascalCase for components

## Import Patterns
- **Absolute imports**: Use `@/` prefix for src directory
- **Relative imports**: For closely related files
- **Type imports**: Use `import type` for TypeScript types
- **Component imports**: Group by source (React, UI libs, local)

## File Organization Principles
- **Feature-based**: Group related functionality together
- **Separation of concerns**: UI, business logic, and configuration separated
- **Reusability**: Common components in shared directories
- **Type safety**: Strong TypeScript interfaces throughout