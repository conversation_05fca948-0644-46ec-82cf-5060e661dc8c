# Technology Stack

## Framework & Build System
- **Plasmo Framework**: Browser extension development framework (v0.90.5)
- **React 18.2.0**: UI library with TypeScript support
- **TypeScript 5.3.3**: Primary development language
- **Tailwind CSS**: Utility-first CSS framework with custom design system

## UI Components & Libraries
- **Radix UI**: Headless component primitives (@radix-ui/react-*)
- **Lucide React**: Icon library
- **Shadcn/ui**: Component system built on Radix UI
- **React Markdown**: Markdown rendering with remark-gfm support
- **Shiki**: Syntax highlighting for code blocks

## Development Tools
- **Prettier**: Code formatting with import sorting
- **PostCSS**: CSS processing with Autoprefixer
- **PNPM**: Package manager (preferred over npm/yarn)

## Common Commands

### Development
```bash
# Start development server
pnpm dev

# Build for production
pnpm build

# Package extension for distribution
pnpm package
```

### Code Quality
```bash
# Format code
npx prettier --write .

# Type checking
npx tsc --noEmit
```

## Architecture Patterns
- **Custom Hooks**: Business logic abstraction (useChatbotAPI, useChatbotUnificado)
- **Component Composition**: Modular UI components with clear separation
- **Configuration Management**: localStorage-based settings persistence
- **Error Boundaries**: Graceful error handling with retry logic
- **TypeScript Interfaces**: Strong typing for API responses and data structures

## API Integration
- **Dual Connectivity**: Supports both REST API and webhook endpoints
- **Retry Logic**: Exponential backoff for failed requests
- **AbortController**: Request timeout handling
- **LocalStorage**: Session and configuration persistence

## Browser Extension Specifics
- **Manifest V3**: Modern extension architecture
- **Popup Interface**: Main UI rendered in extension popup
- **Host Permissions**: Broad web access for API calls
- **Content Security Policy**: Secure execution environment