# Frontend para Chatbot SOGE - FibraEpsilon

Este documento explica como implementar um frontend para interagir com o chatbot SOGE da FibraEpsilon.

## 📋 Visão Geral

O chatbot SOGE oferece uma API REST completa para:
- Conversas interativas com contexto
- Análise técnica de problemas de streaming
- Gerenciamento de sessões de chat
- Histórico de conversas

## 🚀 Configuração da API

### URL Base
```
http://localhost:8000
```

### Endpoints Principais

| Método | Endpoint | Descrição |
|--------|----------|-----------|
| POST | `/chat/mensagem` | Enviar mensagem para o chatbot |
| GET | `/chat/sessoes` | Listar sessões de chat |
| GET | `/chat/sessoes/{id}/historico` | Obter histórico de uma sessão |
| POST | `/chat/sessoes/{id}/finalizar` | Finalizar uma sessão |

## 💬 Implementação Básica

### 1. Enviar Mensagem

```javascript
async function enviarMensagem(mensagem, sessaoId = null, usuarioId = null) {
    const response = await fetch('http://localhost:8000/chat/mensagem', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify({
            sessao_id: sessaoId,
            mensagem: mensagem,
            usuario_id: usuarioId,
            contexto_adicional: {
                timestamp: new Date().toISOString(),
                fonte: 'frontend_web'
            }
        })
    });
    
    if (!response.ok) {
        throw new Error(`Erro: ${response.status}`);
    }
    
    return await response.json();
}
```

### 2. Exemplo de Uso

```javascript
// Primeira mensagem (cria nova sessão automaticamente)
const resposta1 = await enviarMensagem(
    "Olá! Estou com problema no Disney+",
    null, // sem sessão = cria nova
    "user123"
);

console.log('Sessão criada:', resposta1.sessao_id);
console.log('Resposta:', resposta1.mensagem_assistente.conteudo);

// Mensagens subsequentes (usa a mesma sessão)
const resposta2 = await enviarMensagem(
    "O erro aparece quando tento fazer login",
    resposta1.sessao_id,
    "user123"
);

console.log('Resposta:', resposta2.mensagem_assistente.conteudo);
```

### 3. Obter Histórico

```javascript
async function obterHistorico(sessaoId, limite = 50) {
    const response = await fetch(
        `http://localhost:8000/chat/sessoes/${sessaoId}/historico?limite=${limite}`
    );
    
    if (!response.ok) {
        throw new Error(`Erro: ${response.status}`);
    }
    
    const data = await response.json();
    return data.mensagens;
}
```

### 4. Listar Sessões

```javascript
async function listarSessoes(usuarioId = null, limite = 20) {
    const params = new URLSearchParams();
    if (usuarioId) params.append('usuario_id', usuarioId);
    params.append('limite', limite.toString());
    
    const response = await fetch(
        `http://localhost:8000/chat/sessoes?${params}`
    );
    
    if (!response.ok) {
        throw new Error(`Erro: ${response.status}`);
    }
    
    const data = await response.json();
    return data.sessoes;
}
```

## 🎨 Exemplo de Interface React

### Componente Principal

```jsx
import React, { useState, useEffect, useRef } from 'react';

const ChatbotSOGE = () => {
    const [mensagens, setMensagens] = useState([]);
    const [inputMensagem, setInputMensagem] = useState('');
    const [sessaoId, setSessaoId] = useState(null);
    const [carregando, setCarregando] = useState(false);
    const messagesEndRef = useRef(null);

    const scrollToBottom = () => {
        messagesEndRef.current?.scrollIntoView({ behavior: "smooth" });
    };

    useEffect(() => {
        scrollToBottom();
    }, [mensagens]);

    const enviarMensagem = async () => {
        if (!inputMensagem.trim() || carregando) return;

        const novaMensagem = {
            tipo: 'user',
            conteudo: inputMensagem,
            timestamp: new Date().toISOString()
        };

        setMensagens(prev => [...prev, novaMensagem]);
        setInputMensagem('');
        setCarregando(true);

        try {
            const response = await fetch('http://localhost:8000/chat/mensagem', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    sessao_id: sessaoId,
                    mensagem: inputMensagem,
                    usuario_id: 'user_web',
                    contexto_adicional: {
                        fonte: 'react_frontend',
                        timestamp: new Date().toISOString()
                    }
                })
            });

            if (!response.ok) {
                throw new Error(`Erro: ${response.status}`);
            }

            const data = await response.json();
            
            // Atualizar sessão se for a primeira mensagem
            if (!sessaoId) {
                setSessaoId(data.sessao_id);
            }

            // Adicionar resposta do assistente
            const respostaAssistente = {
                tipo: 'assistant',
                conteudo: data.mensagem_assistente.conteudo,
                timestamp: data.mensagem_assistente.timestamp
            };

            setMensagens(prev => [...prev, respostaAssistente]);

        } catch (error) {
            console.error('Erro ao enviar mensagem:', error);
            const mensagemErro = {
                tipo: 'assistant',
                conteudo: 'Desculpe, ocorreu um erro. Tente novamente.',
                timestamp: new Date().toISOString()
            };
            setMensagens(prev => [...prev, mensagemErro]);
        } finally {
            setCarregando(false);
        }
    };

    const handleKeyPress = (e) => {
        if (e.key === 'Enter' && !e.shiftKey) {
            e.preventDefault();
            enviarMensagem();
        }
    };

    return (
        <div className="chatbot-container">
            <div className="chat-header">
                <h3>🤖 Assistente SOGE - FibraEpsilon</h3>
                {sessaoId && <small>Sessão: {sessaoId.slice(0, 8)}...</small>}
            </div>
            
            <div className="chat-messages">
                {mensagens.map((msg, index) => (
                    <div key={index} className={`message ${msg.tipo}`}>
                        <div className="message-content">
                            {msg.conteudo}
                        </div>
                        <div className="message-time">
                            {new Date(msg.timestamp).toLocaleTimeString()}
                        </div>
                    </div>
                ))}
                {carregando && (
                    <div className="message assistant">
                        <div className="message-content">
                            <div className="typing-indicator">
                                <span></span><span></span><span></span>
                            </div>
                        </div>
                    </div>
                )}
                <div ref={messagesEndRef} />
            </div>
            
            <div className="chat-input">
                <textarea
                    value={inputMensagem}
                    onChange={(e) => setInputMensagem(e.target.value)}
                    onKeyPress={handleKeyPress}
                    placeholder="Digite sua mensagem..."
                    disabled={carregando}
                    rows={2}
                />
                <button 
                    onClick={enviarMensagem}
                    disabled={carregando || !inputMensagem.trim()}
                >
                    {carregando ? '⏳' : '📤'}
                </button>
            </div>
        </div>
    );
};

export default ChatbotSOGE;
```

## 🎨 CSS Básico

```css
.chatbot-container {
    max-width: 800px;
    margin: 0 auto;
    border: 1px solid #ddd;
    border-radius: 10px;
    overflow: hidden;
    height: 600px;
    display: flex;
    flex-direction: column;
}

.chat-header {
    background: #2c3e50;
    color: white;
    padding: 15px;
    text-align: center;
}

.chat-header h3 {
    margin: 0;
    font-size: 1.2em;
}

.chat-header small {
    opacity: 0.8;
    font-size: 0.8em;
}

.chat-messages {
    flex: 1;
    overflow-y: auto;
    padding: 20px;
    background: #f8f9fa;
}

.message {
    margin-bottom: 15px;
    display: flex;
    flex-direction: column;
}

.message.user {
    align-items: flex-end;
}

.message.assistant {
    align-items: flex-start;
}

.message-content {
    max-width: 70%;
    padding: 12px 16px;
    border-radius: 18px;
    word-wrap: break-word;
}

.message.user .message-content {
    background: #007bff;
    color: white;
}

.message.assistant .message-content {
    background: white;
    border: 1px solid #ddd;
    color: #333;
}

.message-time {
    font-size: 0.75em;
    color: #666;
    margin-top: 5px;
    padding: 0 10px;
}

.chat-input {
    display: flex;
    padding: 15px;
    background: white;
    border-top: 1px solid #ddd;
}

.chat-input textarea {
    flex: 1;
    border: 1px solid #ddd;
    border-radius: 20px;
    padding: 10px 15px;
    resize: none;
    font-family: inherit;
}

.chat-input button {
    margin-left: 10px;
    padding: 10px 15px;
    border: none;
    border-radius: 50%;
    background: #007bff;
    color: white;
    cursor: pointer;
    font-size: 1.2em;
}

.chat-input button:disabled {
    background: #ccc;
    cursor: not-allowed;
}

.typing-indicator {
    display: flex;
    gap: 4px;
}

.typing-indicator span {
    width: 8px;
    height: 8px;
    border-radius: 50%;
    background: #999;
    animation: typing 1.4s infinite ease-in-out;
}

.typing-indicator span:nth-child(2) {
    animation-delay: 0.2s;
}

.typing-indicator span:nth-child(3) {
    animation-delay: 0.4s;
}

@keyframes typing {
    0%, 60%, 100% {
        transform: translateY(0);
    }
    30% {
        transform: translateY(-10px);
    }
}
```

## 📱 Implementação Vanilla JavaScript

### HTML Básico

```html
<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Chatbot SOGE - FibraEpsilon</title>
    <link rel="stylesheet" href="styles.css">
</head>
<body>
    <div id="chatbot-container" class="chatbot-container">
        <div class="chat-header">
            <h3>🤖 Assistente SOGE - FibraEpsilon</h3>
            <small id="session-info">Iniciando nova conversa...</small>
        </div>

        <div id="chat-messages" class="chat-messages">
            <div class="message assistant">
                <div class="message-content">
                    Olá! Sou o assistente SOGE da FibraEpsilon. Como posso ajudá-lo hoje com questões de aplicativos de streaming?
                </div>
            </div>
        </div>

        <div class="chat-input">
            <textarea
                id="message-input"
                placeholder="Digite sua mensagem..."
                rows="2"
            ></textarea>
            <button id="send-button">📤</button>
        </div>
    </div>

    <script src="chatbot.js"></script>
</body>
</html>
```

### JavaScript Completo

```javascript
class ChatbotSOGE {
    constructor() {
        this.apiUrl = 'http://localhost:8000';
        this.sessaoId = null;
        this.usuarioId = 'user_' + Date.now();
        this.carregando = false;

        this.initElements();
        this.bindEvents();
    }

    initElements() {
        this.messagesContainer = document.getElementById('chat-messages');
        this.messageInput = document.getElementById('message-input');
        this.sendButton = document.getElementById('send-button');
        this.sessionInfo = document.getElementById('session-info');
    }

    bindEvents() {
        this.sendButton.addEventListener('click', () => this.enviarMensagem());
        this.messageInput.addEventListener('keypress', (e) => {
            if (e.key === 'Enter' && !e.shiftKey) {
                e.preventDefault();
                this.enviarMensagem();
            }
        });
    }

    async enviarMensagem() {
        const mensagem = this.messageInput.value.trim();
        if (!mensagem || this.carregando) return;

        // Adicionar mensagem do usuário
        this.adicionarMensagem(mensagem, 'user');
        this.messageInput.value = '';
        this.setCarregando(true);

        try {
            const response = await fetch(`${this.apiUrl}/chat/mensagem`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    sessao_id: this.sessaoId,
                    mensagem: mensagem,
                    usuario_id: this.usuarioId,
                    contexto_adicional: {
                        fonte: 'vanilla_js_frontend',
                        timestamp: new Date().toISOString(),
                        user_agent: navigator.userAgent
                    }
                })
            });

            if (!response.ok) {
                throw new Error(`Erro HTTP: ${response.status}`);
            }

            const data = await response.json();

            // Atualizar sessão se for a primeira mensagem
            if (!this.sessaoId) {
                this.sessaoId = data.sessao_id;
                this.sessionInfo.textContent = `Sessão: ${this.sessaoId.slice(0, 8)}...`;
            }

            // Adicionar resposta do assistente
            this.adicionarMensagem(data.mensagem_assistente.conteudo, 'assistant');

        } catch (error) {
            console.error('Erro ao enviar mensagem:', error);
            this.adicionarMensagem(
                'Desculpe, ocorreu um erro. Tente novamente em alguns instantes.',
                'assistant'
            );
        } finally {
            this.setCarregando(false);
        }
    }

    adicionarMensagem(conteudo, tipo) {
        const messageDiv = document.createElement('div');
        messageDiv.className = `message ${tipo}`;

        const contentDiv = document.createElement('div');
        contentDiv.className = 'message-content';
        contentDiv.textContent = conteudo;

        const timeDiv = document.createElement('div');
        timeDiv.className = 'message-time';
        timeDiv.textContent = new Date().toLocaleTimeString();

        messageDiv.appendChild(contentDiv);
        messageDiv.appendChild(timeDiv);

        this.messagesContainer.appendChild(messageDiv);
        this.scrollToBottom();
    }

    setCarregando(carregando) {
        this.carregando = carregando;
        this.sendButton.disabled = carregando;
        this.messageInput.disabled = carregando;

        if (carregando) {
            this.mostrarIndicadorDigitacao();
        } else {
            this.removerIndicadorDigitacao();
        }
    }

    mostrarIndicadorDigitacao() {
        const typingDiv = document.createElement('div');
        typingDiv.className = 'message assistant typing-message';
        typingDiv.innerHTML = `
            <div class="message-content">
                <div class="typing-indicator">
                    <span></span><span></span><span></span>
                </div>
            </div>
        `;

        this.messagesContainer.appendChild(typingDiv);
        this.scrollToBottom();
    }

    removerIndicadorDigitacao() {
        const typingMessage = this.messagesContainer.querySelector('.typing-message');
        if (typingMessage) {
            typingMessage.remove();
        }
    }

    scrollToBottom() {
        this.messagesContainer.scrollTop = this.messagesContainer.scrollHeight;
    }

    async carregarHistorico() {
        if (!this.sessaoId) return;

        try {
            const response = await fetch(
                `${this.apiUrl}/chat/sessoes/${this.sessaoId}/historico`
            );

            if (!response.ok) return;

            const data = await response.json();

            // Limpar mensagens atuais
            this.messagesContainer.innerHTML = '';

            // Adicionar mensagens do histórico
            data.mensagens.forEach(msg => {
                this.adicionarMensagem(msg.conteudo, msg.tipo);
            });

        } catch (error) {
            console.error('Erro ao carregar histórico:', error);
        }
    }

    async finalizarSessao() {
        if (!this.sessaoId) return;

        try {
            await fetch(`${this.apiUrl}/chat/sessoes/${this.sessaoId}/finalizar`, {
                method: 'POST'
            });

            this.sessaoId = null;
            this.sessionInfo.textContent = 'Sessão finalizada';

        } catch (error) {
            console.error('Erro ao finalizar sessão:', error);
        }
    }
}

// Inicializar chatbot quando a página carregar
document.addEventListener('DOMContentLoaded', () => {
    window.chatbot = new ChatbotSOGE();
});

// Finalizar sessão quando sair da página
window.addEventListener('beforeunload', () => {
    if (window.chatbot) {
        window.chatbot.finalizarSessao();
    }
});
```

## 🔧 Configuração de CORS

Para que o frontend funcione corretamente, certifique-se de que o arquivo `.env` do backend inclua as origens corretas:

```env
# CORS Configuration
ALLOWED_ORIGINS=["http://localhost:3000", "http://localhost:8080", "http://127.0.0.1:5500", "*"]
```

## 📊 Estrutura de Dados da API

### Resposta de Mensagem

```json
{
    "sucesso": true,
    "sessao_id": "uuid-da-sessao",
    "mensagem_usuario": {
        "id": "uuid-mensagem-user",
        "sessao_id": "uuid-da-sessao",
        "tipo": "user",
        "conteudo": "Mensagem do usuário",
        "timestamp": "2024-01-01T12:00:00Z"
    },
    "mensagem_assistente": {
        "id": "uuid-mensagem-assistant",
        "sessao_id": "uuid-da-sessao",
        "tipo": "assistant",
        "conteudo": "Resposta do assistente",
        "timestamp": "2024-01-01T12:00:01Z"
    },
    "tempo_processamento": 1.23
}
```

### Histórico de Sessão

```json
{
    "sucesso": true,
    "sessao": {
        "id": "uuid-da-sessao",
        "usuario_id": "user123",
        "titulo": "Conversa sobre Disney+",
        "status": "ativa",
        "criada_em": "2024-01-01T12:00:00Z",
        "atualizada_em": "2024-01-01T12:05:00Z"
    },
    "total_mensagens": 6,
    "mensagens": [
        {
            "id": "uuid-msg-1",
            "tipo": "user",
            "conteudo": "Olá!",
            "timestamp": "2024-01-01T12:00:00Z"
        },
        {
            "id": "uuid-msg-2",
            "tipo": "assistant",
            "conteudo": "Olá! Como posso ajudá-lo?",
            "timestamp": "2024-01-01T12:00:01Z"
        }
    ]
}
```

## 🚀 Deploy e Produção

### Variáveis de Ambiente para Produção

```javascript
const config = {
    apiUrl: process.env.NODE_ENV === 'production'
        ? 'https://sua-api.com'
        : 'http://localhost:8000',

    // Outras configurações
    maxRetries: 3,
    timeout: 30000,
    enableLogging: process.env.NODE_ENV !== 'production'
};
```

### Tratamento de Erros Avançado

```javascript
class ApiError extends Error {
    constructor(message, status, details) {
        super(message);
        this.status = status;
        this.details = details;
    }
}

async function apiRequest(url, options = {}) {
    const maxRetries = 3;
    let lastError;

    for (let i = 0; i < maxRetries; i++) {
        try {
            const response = await fetch(url, {
                timeout: 30000,
                ...options
            });

            if (!response.ok) {
                const errorData = await response.json().catch(() => ({}));
                throw new ApiError(
                    errorData.detail || `Erro HTTP ${response.status}`,
                    response.status,
                    errorData
                );
            }

            return await response.json();

        } catch (error) {
            lastError = error;

            // Não retry em erros 4xx (exceto 429)
            if (error.status >= 400 && error.status < 500 && error.status !== 429) {
                throw error;
            }

            // Aguardar antes de tentar novamente
            if (i < maxRetries - 1) {
                await new Promise(resolve => setTimeout(resolve, 1000 * (i + 1)));
            }
        }
    }

    throw lastError;
}
```

## 📱 Responsividade Mobile

```css
@media (max-width: 768px) {
    .chatbot-container {
        height: 100vh;
        border-radius: 0;
        border: none;
    }

    .message-content {
        max-width: 85%;
    }

    .chat-input textarea {
        font-size: 16px; /* Evita zoom no iOS */
    }
}
```

## 🔍 Debugging e Logs

```javascript
const logger = {
    debug: (message, data) => {
        if (config.enableLogging) {
            console.log(`[CHATBOT DEBUG] ${message}`, data);
        }
    },

    error: (message, error) => {
        console.error(`[CHATBOT ERROR] ${message}`, error);

        // Enviar para serviço de monitoramento em produção
        if (process.env.NODE_ENV === 'production') {
            // Sentry, LogRocket, etc.
        }
    }
};
```

## 🔐 Autenticação com Supabase

### Configuração Inicial

1. **Criar projeto no Supabase**:
   - Acesse [supabase.com](https://supabase.com)
   - Crie um novo projeto
   - Anote a URL e a chave anônima

2. **Configurar variáveis de ambiente**:
   ```bash
   # Copie o arquivo de exemplo
   cp .env.example .env

   # Edite com suas credenciais do Supabase
   PLASMO_PUBLIC_SUPABASE_URL=https://seu-projeto.supabase.co
   PLASMO_PUBLIC_SUPABASE_ANON_KEY=sua-chave-anonima
   ```

3. **Instalar dependências**:
   ```bash
   pnpm add @supabase/supabase-js
   ```

### Estrutura de Autenticação

O projeto inclui:
- **Hook `useAuth`**: Gerencia estado de autenticação
- **Componente `Auth`**: Interface de login/registro
- **Componente `AuthWrapper`**: Protege o acesso ao chat
- **Configuração Supabase**: Cliente configurado para extensões Chrome

### Funcionalidades Implementadas

- ✅ Login com email/senha
- ✅ Registro de novos usuários
- ✅ Recuperação de senha
- ✅ Persistência de sessão
- ✅ Logout
- ✅ Interface responsiva
- ✅ Tratamento de erros

### Configuração no Supabase Dashboard

1. **Habilitar autenticação por email**:
   - Vá em Authentication > Settings
   - Habilite "Enable email confirmations" se desejar

2. **Configurar URLs de redirecionamento**:
   - Adicione `chrome-extension://[seu-extension-id]/popup.html`
   - Para desenvolvimento local: `http://localhost:1012`

### Uso da Autenticação

```typescript
import { useAuth } from './hooks/useAuth'

function MeuComponente() {
  const { user, signIn, signOut, loading } = useAuth()

  if (loading) return <div>Carregando...</div>

  if (!user) {
    return <Auth />
  }

  return (
    <div>
      <p>Bem-vindo, {user.email}!</p>
      <button onClick={signOut}>Sair</button>
    </div>
  )
}
```

## 🎯 Próximos Passos

1. ✅ **Implementar autenticação** de usuários
2. **Adicionar upload de arquivos** para análise
3. **Implementar notificações** push
4. **Criar dashboard** de administração
5. **Adicionar temas** personalizáveis
6. **Implementar busca** no histórico
7. **Adicionar exportação** de conversas

## 📞 Suporte

Para dúvidas sobre a implementação do frontend:
- Consulte a documentação da API em `/docs`
- Verifique os logs do backend para debugging
- Teste os endpoints usando ferramentas como Postman
```