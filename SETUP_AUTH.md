# 🔐 Configuração de Autenticação com Supabase

Este guia explica como configurar a autenticação com Supabase no projeto Fibra Epsilon.

## 📋 Pré-requisitos

- Conta no [Supabase](https://supabase.com)
- Node.js e pnpm instalados
- Projeto Fibra Epsilon clonado

## 🚀 Passo a Passo

### 1. Criar Projeto no Supabase

1. Acesse [supabase.com](https://supabase.com) e faça login
2. Clique em "New Project"
3. Escolha uma organização
4. Defina:
   - **Name**: `fibra-epsilon-chat`
   - **Database Password**: Crie uma senha segura
   - **Region**: Escolha a mais próxima (ex: South America)
5. Clique em "Create new project"
6. Aguarde a criação (pode levar alguns minutos)

### 2. Obter Credenciais

1. No dashboard do projeto, vá em **Settings** > **API**
2. <PERSON><PERSON> as seguintes informações:
   - **Project URL**: `https://[seu-projeto-id].supabase.co`
   - **anon public key**: Chave que começa com `eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...`

### 3. Configurar Variáveis de Ambiente

1. Na raiz do projeto, copie o arquivo de exemplo:
   ```bash
   cp .env.example .env
   ```

2. Edite o arquivo `.env` com suas credenciais:
   ```env
   PLASMO_PUBLIC_SUPABASE_URL=https://seu-projeto-id.supabase.co
   PLASMO_PUBLIC_SUPABASE_ANON_KEY=sua-chave-anonima-aqui
   ```

### 4. Configurar Autenticação no Supabase

1. No dashboard, vá em **Authentication** > **Settings**
2. Em **Site URL**, adicione:
   - Para desenvolvimento: `http://localhost:1012`
   - Para extensão: `chrome-extension://[extension-id]/popup.html`

3. Em **Redirect URLs**, adicione as mesmas URLs acima

4. Configure os provedores desejados:
   - **Email**: Já habilitado por padrão
   - **Google** (opcional): Configure OAuth se desejar

### 5. Testar a Instalação

1. Instale as dependências (se ainda não fez):
   ```bash
   pnpm install
   ```

2. Execute o projeto:
   ```bash
   pnpm dev
   ```

3. Abra a extensão e teste:
   - Criar uma nova conta
   - Fazer login
   - Testar recuperação de senha

## 🔧 Configurações Avançadas

### Personalizar Emails

1. Vá em **Authentication** > **Email Templates**
2. Personalize os templates de:
   - Confirmação de email
   - Recuperação de senha
   - Convite de usuário

### Configurar RLS (Row Level Security)

Se você planeja armazenar dados do chat no Supabase:

```sql
-- Criar tabela de sessões de chat
CREATE TABLE chat_sessions (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
  title TEXT,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Habilitar RLS
ALTER TABLE chat_sessions ENABLE ROW LEVEL SECURITY;

-- Política: usuários só veem suas próprias sessões
CREATE POLICY "Users can view own sessions" ON chat_sessions
  FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Users can insert own sessions" ON chat_sessions
  FOR INSERT WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can update own sessions" ON chat_sessions
  FOR UPDATE USING (auth.uid() = user_id);
```

## 🐛 Solução de Problemas

### Erro: "Invalid API key"
- Verifique se copiou a chave correta do dashboard
- Certifique-se de usar `PLASMO_PUBLIC_` no prefixo da variável

### Erro: "Invalid redirect URL"
- Adicione a URL no dashboard do Supabase
- Para extensões Chrome, use o formato correto com extension ID

### Erro: "Network request failed"
- Verifique sua conexão com internet
- Confirme se a URL do projeto está correta

### Usuário não consegue fazer login
- Verifique se o email foi confirmado (se habilitado)
- Teste com um email diferente
- Verifique os logs no dashboard do Supabase

## 📱 Testando a Extensão

1. **Build da extensão**:
   ```bash
   pnpm build
   ```

2. **Carregar no Chrome**:
   - Abra `chrome://extensions/`
   - Ative "Modo do desenvolvedor"
   - Clique em "Carregar sem compactação"
   - Selecione a pasta `build/chrome-mv3-prod`

3. **Testar autenticação**:
   - Clique no ícone da extensão
   - Teste login/registro
   - Verifique se o chat aparece após login

## 🔒 Segurança

### Boas Práticas

1. **Nunca exponha a service role key** no frontend
2. **Use RLS** para proteger dados no banco
3. **Configure CORS** adequadamente
4. **Monitore logs** de autenticação
5. **Use HTTPS** em produção

### Monitoramento

No dashboard do Supabase:
- **Authentication** > **Users**: Ver usuários registrados
- **Authentication** > **Logs**: Monitorar tentativas de login
- **Settings** > **API**: Verificar uso da API

## 📞 Suporte

Se encontrar problemas:

1. Verifique a [documentação do Supabase](https://supabase.com/docs)
2. Consulte os logs no dashboard
3. Teste as credenciais manualmente
4. Verifique as configurações de CORS e redirect URLs

## ✅ Checklist de Configuração

- [ ] Projeto criado no Supabase
- [ ] Credenciais copiadas
- [ ] Arquivo `.env` configurado
- [ ] URLs de redirect configuradas
- [ ] Dependências instaladas
- [ ] Teste de login funcionando
- [ ] Teste de registro funcionando
- [ ] Teste de recuperação de senha funcionando
- [ ] Extensão carregada no Chrome
- [ ] Autenticação funcionando na extensão
