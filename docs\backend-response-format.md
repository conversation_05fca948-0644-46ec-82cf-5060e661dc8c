# Formato de Resposta para o Agente de IA SOGE

Este documento descreve como o backend deve formatar as respostas para o agente de IA SOGE para garantir uma apresentação adequada no frontend.

## Formatação Suportada

O frontend suporta formatação Markdown básica nas respostas do assistente. Use os seguintes formatos:

### Texto com Ênfase

```markdown
**Texto em negrito** - para destacar informações importantes
*Texto em itálico* - para ênfase suave
```

### Listas

Use listas para organizar informações:

```markdown
- Item da lista 1
- Item da lista 2
- Item da lista 3
```

### Código Inline

Para comandos ou valores específicos:

```markdown
Use o comando `ping *******` para testar conectividade
```

### Blocos de Código

Para logs, configurações ou scripts:

````markdown
```
2024-01-15 10:30:45 [ERROR] Connection timeout
2024-01-15 10:30:46 [INFO] Retrying connection...
2024-01-15 10:30:47 [SUCCESS] Connection established
```
````

## Exemplos de Respostas Bem Formatadas

### Análise de Problema de Conectividade

```markdown
**Análise do Problema de Conectividade**

Identifiquei os seguintes pontos no log fornecido:

- **Timeout de conexão** detectado às 10:30:45
- **Tentativa de reconexão** automática
- **Sucesso na reconexão** após 2 segundos

**Recomendações:**

- Verificar latência da rede com `ping -t *******`
- Monitorar logs de firewall
- Considerar ajustar timeout para 30 segundos

**Próximos passos:**
1. Execute o diagnóstico de rede
2. Verifique configurações de proxy
3. Teste em horário de menor tráfego
```

### Interpretação de Logs

```markdown
**Interpretação do Log de Sistema**

O log indica um padrão típico de **sobrecarga de rede**:

```
[WARN] Buffer overflow detected - Queue: 1024/1024
[ERROR] Packet drop rate: 15.3%
[INFO] Switching to backup route
```

**Causa provável:** Saturação do buffer de rede

**Soluções recomendadas:**
- Aumentar tamanho do buffer: `net.core.rmem_max = 134217728`
- Implementar QoS para priorizar tráfego crítico
- Considerar upgrade de largura de banda
```

### Suporte Técnico Estruturado

```markdown
**Diagnóstico Completo - Protocolo RTMP**

**Status atual:** *Conexão instável*

**Parâmetros analisados:**
- Bitrate: 2500 kbps (dentro do limite)
- Latência: 45ms (aceitável)
- Packet loss: 3.2% (**acima do recomendado**)

**Configuração recomendada:**

```
rtmp {
    server {
        listen 1935;
        chunk_size 4096;
        max_connections 1000;
    }
}
```

**Ações imediatas:**
1. Reduzir bitrate para 2000 kbps
2. Verificar rota de rede
3. Monitorar por 30 minutos
```

## Diretrizes Gerais

### ✅ Faça

- Use **negrito** para destacar informações críticas
- Organize informações em listas quando apropriado
- Inclua blocos de código para logs e configurações
- Estruture respostas com seções claras
- Use linguagem técnica precisa mas acessível

### ❌ Evite

- Parágrafos muito longos sem formatação
- Texto corrido sem estrutura
- Informações importantes sem destaque
- Códigos ou logs sem formatação adequada
- Respostas genéricas sem detalhes técnicos

## Estrutura Recomendada para Respostas

```markdown
**[Título da Análise/Diagnóstico]**

[Resumo breve do problema/situação]

**Detalhes técnicos:**
- Ponto 1
- Ponto 2
- Ponto 3

**Código/Log relevante:**
```
[código ou log aqui]
```

**Recomendações:**
1. Ação imediata
2. Ação de médio prazo
3. Monitoramento contínuo

**Próximos passos:**
[Orientações claras para o usuário]
```

## Notas Técnicas

- O frontend renderiza automaticamente a formatação Markdown
- Quebras de linha duplas criam parágrafos separados
- Use ``` para blocos de código multilinha
- Use ` para código inline
- Listas são renderizadas com bullets automáticos

## Exemplo de Resposta JSON

```json
{
  "mensagem_assistente": {
    "id": "msg_123",
    "conteudo": "**Análise Completa**\n\nIdentifiquei o problema:\n\n- Timeout de conexão\n- Buffer overflow\n\n```\nERROR: Connection timeout after 30s\n```\n\n**Solução:** Ajustar timeout para 60s",
    "timestamp": "2024-01-15T10:30:00Z"
  }
}
```

---

*Este documento deve ser seguido por todos os desenvolvedores do backend para garantir consistência na apresentação das respostas do agente SOGE.*
