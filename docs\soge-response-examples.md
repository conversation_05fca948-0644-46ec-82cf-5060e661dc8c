# Exemplos de Respostas SOGE por Cenário

Este documento contém exemplos específicos de como o agente SOGE deve responder em diferentes cenários técnicos.

## Cenário 1: Análise de Log de Erro

**Input do usuário:** "Estou recebendo este erro no meu servidor de streaming..."

**Resposta SOGE:**

```markdown
**Análise do Log de Erro - Servidor de Streaming**

Identifiquei **3 problemas críticos** no log fornecido:

**Problemas encontrados:**
- **Memory leak** no processo ffmpeg (PID 1234)
- **Timeout de conexão** com CDN principal
- **Buffer underrun** no encoder de vídeo

**Detalhes técnicos:**

```
[ERROR] ffmpeg[1234]: Memory usage: 8.2GB (limit: 8GB)
[WARN] CDN timeout: primary.cdn.com (30s)
[CRITICAL] Video buffer: 0.2s remaining (min: 2s)
```

**Soluções imediatas:**
1. Reiniciar processo ffmpeg: `sudo systemctl restart ffmpeg-service`
2. Alternar para CDN secundário
3. Reduzir qualidade temporariamente para 720p

**Monitoramento:**
- Verificar uso de memória a cada 5 minutos
- Testar conectividade CDN: `ping -c 10 primary.cdn.com`
- Monitorar buffer de vídeo em tempo real
```

## Cenário 2: Configuração de Rede

**Input do usuário:** "Como configurar QoS para priorizar tráfego de streaming?"

**Resposta SOGE:**

```markdown
**Configuração de QoS para Streaming**

Para priorizar tráfego de streaming, configure as seguintes regras:

**1. Identificação do tráfego:**

```bash
# Marcar pacotes RTMP (porta 1935)
iptables -t mangle -A OUTPUT -p tcp --dport 1935 -j MARK --set-mark 1

# Marcar pacotes HLS (porta 80/443)
iptables -t mangle -A OUTPUT -p tcp --dport 80 -j MARK --set-mark 2
```

**2. Configuração de classes de tráfego:**

```bash
# Criar classes HTB
tc class add dev eth0 parent 1: classid 1:1 htb rate 100mbit
tc class add dev eth0 parent 1:1 classid 1:10 htb rate 80mbit ceil 100mbit prio 1
tc class add dev eth0 parent 1:1 classid 1:20 htb rate 20mbit ceil 40mbit prio 2
```

**3. Aplicar filtros:**

```bash
# Prioridade alta para streaming
tc filter add dev eth0 protocol ip parent 1: prio 1 handle 1 fw classid 1:10
tc filter add dev eth0 protocol ip parent 1: prio 2 handle 2 fw classid 1:20
```

**Validação:**
- Teste latência: `ping -c 100 streaming-server.com`
- Monitore bandwidth: `iftop -i eth0`
- Verifique QoS: `tc -s class show dev eth0`
```

## Cenário 3: Diagnóstico de Performance

**Input do usuário:** "O streaming está com qualidade ruim, como diagnosticar?"

**Resposta SOGE:**

```markdown
**Diagnóstico de Performance de Streaming**

Vamos analisar os **4 pontos críticos** para qualidade de streaming:

**1. Análise de Bandwidth**

```bash
# Teste de velocidade
speedtest-cli --simple

# Monitoramento em tempo real
iftop -n -P -i eth0
```

**2. Verificação de Latência**

```bash
# Latência para servidor de ingest
ping -c 50 ingest.platform.com

# Traceroute para identificar gargalos
traceroute ingest.platform.com
```

**3. Análise de CPU/GPU**

```bash
# Uso de CPU por processo
top -p $(pgrep ffmpeg)

# Status da GPU (NVIDIA)
nvidia-smi -l 1
```

**4. Configuração do Encoder**

Configuração recomendada para **1080p 60fps**:

```
ffmpeg -i input.mp4 \
  -c:v libx264 \
  -preset medium \
  -crf 23 \
  -maxrate 6000k \
  -bufsize 12000k \
  -c:a aac \
  -b:a 128k \
  -f flv rtmp://ingest.platform.com/live/STREAM_KEY
```

**Métricas ideais:**
- **CPU usage:** < 80%
- **GPU usage:** < 90%
- **Bitrate:** 4000-6000 kbps para 1080p
- **Latência:** < 50ms
- **Packet loss:** < 0.1%
```

## Cenário 4: Troubleshooting de Conectividade

**Input do usuário:** "Não consigo conectar no servidor RTMP"

**Resposta SOGE:**

```markdown
**Troubleshooting - Conexão RTMP**

Vamos diagnosticar a conectividade **passo a passo**:

**Etapa 1: Verificação básica de rede**

```bash
# Teste de conectividade
ping rtmp-server.com

# Verificar resolução DNS
nslookup rtmp-server.com

# Teste de porta específica
telnet rtmp-server.com 1935
```

**Etapa 2: Análise de firewall**

```bash
# Verificar regras iptables
iptables -L -n | grep 1935

# Testar com netcat
nc -zv rtmp-server.com 1935
```

**Etapa 3: Teste de autenticação**

```bash
# Teste básico RTMP
ffmpeg -re -i test.mp4 -c copy -f flv rtmp://server.com/live/key
```

**Possíveis causas e soluções:**

| Erro | Causa Provável | Solução |
|------|----------------|---------|
| `Connection refused` | Porta bloqueada | Verificar firewall |
| `Connection timeout` | Rede instável | Testar rota alternativa |
| `Authentication failed` | Chave inválida | Verificar stream key |
| `Protocol error` | Versão incompatível | Atualizar cliente RTMP |

**Comandos de diagnóstico avançado:**

```bash
# Captura de pacotes
tcpdump -i eth0 port 1935 -w rtmp_debug.pcap

# Análise de logs em tempo real
tail -f /var/log/nginx/rtmp_access.log
```
```

## Cenário 5: Otimização de Configuração

**Input do usuário:** "Como otimizar minha configuração para streaming de baixa latência?"

**Resposta SOGE:**

```markdown
**Otimização para Streaming de Baixa Latência**

Para **latência < 1 segundo**, implemente estas configurações:

**1. Configuração do Encoder**

```bash
ffmpeg -f v4l2 -i /dev/video0 \
  -c:v libx264 \
  -preset ultrafast \
  -tune zerolatency \
  -crf 28 \
  -maxrate 2500k \
  -bufsize 2500k \
  -g 30 \
  -keyint_min 30 \
  -c:a aac \
  -b:a 96k \
  -ac 2 \
  -ar 44100 \
  -f flv rtmp://server/live/key
```

**2. Configuração do Servidor (Nginx RTMP)**

```nginx
rtmp {
    server {
        listen 1935;
        chunk_size 1024;
        
        application live {
            live on;
            
            # Configurações de baixa latência
            sync 10ms;
            interleave on;
            wait_key on;
            wait_video on;
            
            # Buffer mínimo
            play_buffer_size 1024k;
            publish_buffer_size 1024k;
        }
    }
}
```

**3. Otimizações de Sistema**

```bash
# Aumentar buffer de rede
echo 'net.core.rmem_max = 134217728' >> /etc/sysctl.conf
echo 'net.core.wmem_max = 134217728' >> /etc/sysctl.conf

# Aplicar configurações
sysctl -p
```

**4. Monitoramento de Latência**

```bash
# Script de monitoramento
#!/bin/bash
while true; do
    echo "$(date): Latency $(ping -c 1 server.com | grep time= | cut -d'=' -f4)"
    sleep 5
done
```

**Métricas alvo:**
- **Encoder latency:** < 100ms
- **Network latency:** < 50ms
- **Buffer size:** 1-2 segundos máximo
- **Keyframe interval:** 2 segundos
```

---

*Estes exemplos devem ser adaptados conforme o contexto específico de cada consulta do usuário.*
