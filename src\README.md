# Chatbot SOGE - Extensão Plasmo

Este é um chatbot integrado para a extensão Plasmo que se conecta com o assistente SOGE da FibraEpsilon para ajudar com problemas de streaming.

## 🚀 Funcionalidades

### ✨ Principais
- **Chat em tempo real** com o assistente SOGE
- **Gerenciamento de sessões** automático
- **Histórico de conversas** persistente
- **Configurações personalizáveis** da API
- **Interface responsiva** otimizada para extensão

### 🔧 Configurações
- URL da API customizável
- Timeout configurável
- Número máximo de tentativas
- Logs de debug opcionais
- ID de usuário personalizado

### 📚 Histórico
- Visualização de todas as conversas
- Detalhes completos de cada sessão
- Carregamento de conversas anteriores
- Finalização de sessões ativas

## 📁 Estrutura dos Arquivos

```
src/
├── components/
│   ├── ChatbotSOGE.tsx          # Componente principal do chatbot
│   ├── ChatbotSOGE.css          # Estilos do chatbot
│   ├── ConfiguracoesChatbot.tsx # Modal de configurações
│   ├── ConfiguracoesChatbot.css # Estilos das configurações
│   ├── HistoricoSessoes.tsx     # Modal de histórico
│   └── HistoricoSessoes.css     # Estilos do histórico
├── hooks/
│   └── useChatbotAPI.ts         # Hook para gerenciar API
└── README.md                    # Esta documentação
```

## 🎯 Como Usar

### 1. Configuração Inicial
1. Abra a extensão
2. Clique no ícone ⚙️ no cabeçalho
3. Configure a URL da API (padrão: `http://localhost:8000`)
4. Ajuste outras configurações conforme necessário
5. Teste a conexão
6. Salve as configurações

### 2. Iniciando uma Conversa
1. Digite sua mensagem no campo de texto
2. Pressione Enter ou clique no botão 📤
3. Aguarde a resposta do assistente
4. Continue a conversa normalmente

### 3. Gerenciando Sessões
- **Nova sessão**: Criada automaticamente na primeira mensagem
- **Finalizar sessão**: Clique no ✕ ao lado do ID da sessão
- **Visualizar histórico**: Clique no ícone 📚 no cabeçalho

### 4. Histórico de Conversas
1. Clique no ícone 📚 para abrir o histórico
2. Visualize todas as suas conversas
3. Clique no ícone 👁️ para ver detalhes
4. Use "📥 Carregar no Chat" para continuar uma conversa

## 🔌 Integração com a API

### Endpoints Utilizados
- `POST /chat/mensagem` - Enviar mensagens
- `GET /chat/sessoes` - Listar sessões
- `GET /chat/sessoes/{id}/historico` - Obter histórico
- `POST /chat/sessoes/{id}/finalizar` - Finalizar sessão

### Formato das Mensagens
```typescript
interface Mensagem {
  id: string;
  tipo: 'user' | 'assistant';
  conteudo: string;
  timestamp: string;
}
```

### Configuração da API
```typescript
interface ConfiguracaoAPI {
  apiUrl: string;        // URL do servidor
  timeout: number;       // Timeout em ms
  maxTentativas: number; // Máximo de tentativas
  habilitarLogs: boolean; // Logs de debug
  usuarioId: string;     // ID único do usuário
}
```

## 🎨 Personalização

### Cores e Temas
O chatbot usa um gradiente azul-roxo por padrão. Para personalizar:

1. Edite `ChatbotSOGE.css`
2. Modifique as variáveis de cor:
```css
.chat-header {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}
```

### Responsividade
O chatbot é totalmente responsivo e se adapta a diferentes tamanhos de tela:
- Desktop: 400px de largura
- Mobile: Largura total da tela

## 🔧 Desenvolvimento

### Dependências
- React 18.2.0
- TypeScript
- Plasmo Framework

### Scripts Disponíveis
```bash
# Desenvolvimento
npm run dev

# Build para produção
npm run build

# Empacotar extensão
npm run package
```

### Estrutura do Hook useChatbotAPI
```typescript
const {
  enviarMensagem,     // Enviar mensagem para API
  obterHistorico,     // Buscar histórico de sessão
  listarSessoes,      // Listar todas as sessões
  finalizarSessao,    // Finalizar sessão ativa
  carregando,         // Estado de carregamento
  erro,               // Mensagem de erro
  limparErro          // Limpar erro atual
} = useChatbotAPI(apiUrl);
```

## 🐛 Tratamento de Erros

### Tipos de Erro
1. **Erro de Conexão**: Servidor indisponível
2. **Erro HTTP**: Status codes 4xx/5xx
3. **Timeout**: Requisição demorou muito
4. **Erro de Parsing**: Resposta inválida

### Retry Logic
- Máximo de 3 tentativas por padrão
- Backoff exponencial (1s, 2s, 3s)
- Não retry em erros 4xx (exceto 429)

## 📱 Recursos Mobile

### Otimizações
- Font-size 16px para evitar zoom no iOS
- Touch targets de pelo menos 44px
- Scrolling suave e natural
- Teclado virtual friendly

### Gestos
- Swipe para scroll no histórico
- Tap para focar no input
- Long press para seleção de texto

## 🔒 Segurança

### Dados Locais
- Configurações salvas no localStorage
- Nenhum dado sensível armazenado
- Limpeza automática de sessões antigas

### Comunicação
- HTTPS recomendado para produção
- Headers de segurança incluídos
- Validação de entrada no frontend

## 📊 Performance

### Otimizações
- Lazy loading de componentes
- Debounce em inputs
- Virtualização de listas longas
- Memoização de componentes pesados

### Métricas
- Tempo de resposta < 2s
- Bundle size < 500KB
- Memory usage < 50MB

## 🚀 Deploy

### Build para Produção
1. Configure variáveis de ambiente
2. Execute `npm run build`
3. Teste a extensão localmente
4. Empacote com `npm run package`
5. Publique na Chrome Web Store

### Variáveis de Ambiente
```env
PLASMO_PUBLIC_API_URL=https://api.fibraepsilon.com
PLASMO_PUBLIC_ENVIRONMENT=production
```

## 📞 Suporte

### Problemas Comuns
1. **Erro de CORS**: Verifique configuração do servidor
2. **Timeout**: Aumente o valor nas configurações
3. **Sessão não encontrada**: Limpe o localStorage
4. **Interface quebrada**: Recarregue a extensão

### Debug
1. Ative logs nas configurações
2. Abra DevTools da extensão
3. Verifique Network tab
4. Analise Console errors

Para mais ajuda, consulte o README_FRONTEND.md principal do projeto.
