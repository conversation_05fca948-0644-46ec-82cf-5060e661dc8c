/* Estilos customizados para o ChatbotSOGE */

/* Scrollbar personalizada para webkit browsers */
.chatMessages::-webkit-scrollbar {
  width: 6px;
}

.chatMessages::-webkit-scrollbar-track {
  background: transparent;
}

.chatMessages::-webkit-scrollbar-thumb {
  background: rgba(203, 213, 225, 0.5);
  border-radius: 3px;
  transition: background 0.2s ease;
}

.chatMessages::-webkit-scrollbar-thumb:hover {
  background: rgba(203, 213, 225, 0.8);
}

/* Animação suave para novas mensagens */
.messageAnimation {
  animation: slideInMessage 0.3s ease-out;
}

@keyframes slideInMessage {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Melhoria na responsividade do popup */
.chatContainer {
  max-height: 90vh;
  max-width: 95vw;
}

@media (max-width: 640px) {
  .chatContainer {
    width: 100vw;
    height: 100vh;
    max-height: 100vh;
    border-radius: 0;
  }
}

/* Indicador de scroll ativo */
.scrollIndicator {
  opacity: 0;
  transition: opacity 0.3s ease;
}

.scrollIndicator.visible {
  opacity: 1;
}

/* Estilo para mensagens longas */
.messageContent {
  word-wrap: break-word;
  overflow-wrap: break-word;
  hyphens: auto;
}

/* Melhoria no foco do textarea */
.messageInput:focus {
  box-shadow: 0 0 0 2px rgba(59, 130, 246, 0.5);
  border-color: rgb(59, 130, 246);
}
