import React, { useState } from 'react';
import { X, TestTube, RotateCcw, Save, Loader2, Webhook, Server, Settings } from 'lucide-react';
import { But<PERSON> } from '@/src/components/ui/button';
import { Input } from '@/src/components/ui/input';
import { Card, CardContent, CardHeader, CardTitle } from '@/src/components/ui/card';
import { Badge } from '@/src/components/ui/badge';
import { Checkbox } from '@/src/components/ui/checkbox';
import { RadioGroup, RadioGroupItem } from '@/src/components/ui/radio-group';
import { Label } from '@/src/components/ui/label';
import { Collapsible, CollapsibleContent, CollapsibleTrigger } from '@/src/components/ui/collapsible';
import type { ConfiguracaoUnificada, TipoConexao, SessionManagementConfig } from '../hooks/useChatbotUnificado';
import { DEFAULT_STORAGE_CONFIG } from '../lib/SessionStorage';

interface ConfiguracoesChatbotProps {
  onClose: () => void;
  onSave: (config: ConfiguracaoUnificada) => void;
  configuracaoAtual: ConfiguracaoUnificada;
}

export interface ConfiguracaoAPI {
  apiUrl: string;
  timeout: number;
  maxTentativas: number;
  habilitarLogs: boolean;
  usuarioId: string;
}

const ConfiguracoesChatbot: React.FC<ConfiguracoesChatbotProps> = ({
  onClose,
  onSave,
  configuracaoAtual
}) => {
  const [config, setConfig] = useState<ConfiguracaoUnificada>(configuracaoAtual);
  const [testando, setTestando] = useState(false);
  const [statusTeste, setStatusTeste] = useState<'idle' | 'success' | 'error'>('idle');
  const [mensagemTeste, setMensagemTeste] = useState('');

  // We'll create the webhook API instance dynamically in the test function
  // since the URL can change during configuration

  const handleInputChange = (campo: keyof ConfiguracaoUnificada, valor: any) => {
    setConfig(prev => ({
      ...prev,
      [campo]: valor
    }));
  };

  const handleSessionManagementChange = (campo: keyof SessionManagementConfig, valor: any) => {
    setConfig(prev => ({
      ...prev,
      sessionManagement: {
        ...prev.sessionManagement,
        [campo]: valor
      }
    }));
  };

  const testarConexao = async () => {
    setTestando(true);
    setStatusTeste('idle');
    setMensagemTeste('');

    try {
      if (config.tipoConexao === 'api') {
        // Test API connection
        const controller = new AbortController();
        const timeoutId = setTimeout(() => controller.abort(), config.timeout);

        const response = await fetch(`${config.apiUrl}/health`, {
          method: 'GET',
          signal: controller.signal,
        });

        clearTimeout(timeoutId);

        if (response.ok) {
          setStatusTeste('success');
          setMensagemTeste('Conexão com API estabelecida com sucesso!');
        } else {
          throw new Error(`Erro HTTP: ${response.status}`);
        }
      } else {
        // Simple webhook connectivity test without using the hook
        const controller = new AbortController();
        const timeoutId = setTimeout(() => controller.abort(), 10000);

        try {
          // First, try a simple HEAD request to check if the server is reachable
          const response = await fetch(config.webhookUrl, {
            method: 'HEAD',
            signal: controller.signal,
          });

          clearTimeout(timeoutId);

          if (response.ok) {
            setStatusTeste('success');
            setMensagemTeste(`Servidor N8N acessível (${response.status})`);
          } else if (response.status === 404) {
            setStatusTeste('error');
            setMensagemTeste('Webhook "chat" não encontrado. Verifique se está configurado no N8N.');
          } else {
            setStatusTeste('error');
            setMensagemTeste(`Servidor respondeu com status ${response.status}`);
          }
        } catch (fetchError) {
          clearTimeout(timeoutId);
          
          if (fetchError instanceof Error) {
            if (fetchError.name === 'AbortError') {
              setStatusTeste('error');
              setMensagemTeste('Timeout - servidor N8N não responde');
            } else if (fetchError.message.includes('fetch')) {
              setStatusTeste('error');
              setMensagemTeste('Não foi possível conectar ao servidor N8N');
            } else {
              setStatusTeste('error');
              setMensagemTeste(`Erro de conectividade: ${fetchError.message}`);
            }
          } else {
            setStatusTeste('error');
            setMensagemTeste('Erro desconhecido de conectividade');
          }
        }
      }
    } catch (error) {
      setStatusTeste('error');
      setMensagemTeste(
        error instanceof Error
          ? `Erro: ${error.message}`
          : 'Erro desconhecido ao testar conexão'
      );
    } finally {
      setTestando(false);
    }
  };

  const handleSave = () => {
    // Validações básicas
    if (config.tipoConexao === 'api' && !config.apiUrl.trim()) {
      alert('URL da API é obrigatória');
      return;
    }

    if (config.tipoConexao === 'webhook' && !config.webhookUrl.trim()) {
      alert('URL do webhook é obrigatória');
      return;
    }

    if (config.timeout < 1000) {
      alert('Timeout deve ser pelo menos 1000ms');
      return;
    }

    if (config.maxTentativas < 1) {
      alert('Máximo de tentativas deve ser pelo menos 1');
      return;
    }

    // Validações para configurações de sessão
    if (config.sessionManagement.enabled) {
      if (config.sessionManagement.maxStoredSessions < 1) {
        alert('Máximo de sessões armazenadas deve ser pelo menos 1');
        return;
      }

      if (config.sessionManagement.maxStoredSessions > 1000) {
        alert('Máximo de sessões armazenadas não deve exceder 1000');
        return;
      }

      if (config.sessionManagement.autoCleanupDays < 1) {
        alert('Dias para limpeza automática deve ser pelo menos 1');
        return;
      }

      if (config.sessionManagement.autoCleanupDays > 365) {
        alert('Dias para limpeza automática não deve exceder 365');
        return;
      }
    }

    onSave(config);
  };

  const resetarPadrao = () => {
    const configPadrao: ConfiguracaoUnificada = {
      tipoConexao: 'webhook',
      apiUrl: 'http://localhost:8000',
      webhookUrl: 'https://n8n.sondtheanime.site/webhook/chat',
      timeout: 30000,
      maxTentativas: 3,
      habilitarLogs: false,
      usuarioId: `user_${Date.now()}`,
      sessionManagement: {
        enabled: true,
        maxStoredSessions: DEFAULT_STORAGE_CONFIG.maxStoredSessions,
        autoCleanupDays: DEFAULT_STORAGE_CONFIG.autoCleanupDays,
        enableDetailedLogging: DEFAULT_STORAGE_CONFIG.enableDetailedLogging,
        compressionEnabled: DEFAULT_STORAGE_CONFIG.compressionEnabled
      }
    };
    setConfig(configPadrao);
  };

  return (
    <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50 backdrop-blur-sm">
      <Card className="w-[90%] max-w-lg max-h-[90vh] overflow-hidden shadow-2xl animate-in slide-in-from-bottom-4 duration-300">
        <CardHeader className="pb-3">
          <div className="flex items-center justify-between">
            <CardTitle className="text-lg">Configurações do Chatbot</CardTitle>
            <Button
              variant="ghost"
              size="icon"
              onClick={onClose}
              className="h-8 w-8"
            >
              <X className="h-4 w-4" />
            </Button>
          </div>
        </CardHeader>

        <CardContent className="max-h-96 overflow-y-auto space-y-5">
          <div className="space-y-3">
            <label className="text-sm font-medium">Tipo de Conexão:</label>
            <RadioGroup
              value={config.tipoConexao}
              onValueChange={(value: TipoConexao) => handleInputChange('tipoConexao', value)}
              className="flex flex-col space-y-2"
            >
              <div className="flex items-center space-x-2">
                <RadioGroupItem value="api" id="api" />
                <Label htmlFor="api" className="flex items-center gap-2 cursor-pointer">
                  <Server className="h-4 w-4" />
                  API Local
                </Label>
              </div>
              <div className="flex items-center space-x-2">
                <RadioGroupItem value="webhook" id="webhook" />
                <Label htmlFor="webhook" className="flex items-center gap-2 cursor-pointer">
                  <Webhook className="h-4 w-4" />
                  Webhook N8N
                </Label>
              </div>
            </RadioGroup>
          </div>

          {config.tipoConexao === 'api' && (
            <div className="space-y-2">
              <label htmlFor="apiUrl" className="text-sm font-medium">URL da API:</label>
              <Input
                id="apiUrl"
                type="url"
                value={config.apiUrl}
                onChange={(e) => handleInputChange('apiUrl', e.target.value)}
                placeholder="http://localhost:8000"
              />
              <p className="text-xs text-muted-foreground">Endereço do servidor do chatbot SOGE</p>
            </div>
          )}

          {config.tipoConexao === 'webhook' && (
            <div className="space-y-2">
              <label htmlFor="webhookUrl" className="text-sm font-medium">URL do Webhook N8N:</label>
              <Input
                id="webhookUrl"
                type="url"
                value={config.webhookUrl}
                onChange={(e) => handleInputChange('webhookUrl', e.target.value)}
                placeholder="https://n8n.sondtheanime.site/webhook/chat"
              />
              <p className="text-xs text-muted-foreground">Endereço do webhook N8N para o agente</p>
            </div>
          )}

          <div className="space-y-2">
            <label htmlFor="usuarioId" className="text-sm font-medium">ID do Usuário:</label>
            <Input
              id="usuarioId"
              type="text"
              value={config.usuarioId}
              onChange={(e) => handleInputChange('usuarioId', e.target.value)}
              placeholder="user_123"
            />
            <p className="text-xs text-muted-foreground">Identificador único para suas conversas</p>
          </div>

          <div className="grid grid-cols-2 gap-4">
            <div className="space-y-2">
              <label htmlFor="timeout" className="text-sm font-medium">Timeout (ms):</label>
              <Input
                id="timeout"
                type="number"
                min="1000"
                max="120000"
                step="1000"
                value={config.timeout}
                onChange={(e) => handleInputChange('timeout', parseInt(e.target.value))}
              />
            </div>

            <div className="space-y-2">
              <label htmlFor="maxTentativas" className="text-sm font-medium">Máx. Tentativas:</label>
              <Input
                id="maxTentativas"
                type="number"
                min="1"
                max="10"
                value={config.maxTentativas}
                onChange={(e) => handleInputChange('maxTentativas', parseInt(e.target.value))}
              />
            </div>
          </div>

          <div className="space-y-2">
            <div className="flex items-center space-x-2">
              <Checkbox
                id="habilitarLogs"
                checked={config.habilitarLogs}
                onCheckedChange={(checked) => handleInputChange('habilitarLogs', checked)}
              />
              <label
                htmlFor="habilitarLogs"
                className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
              >
                Habilitar logs de debug
              </label>
            </div>
            <p className="text-xs text-muted-foreground">Mostra informações detalhadas no console</p>
          </div>

          {/* Configurações de Gerenciamento de Sessão */}
          <Collapsible className="space-y-2">
            <CollapsibleTrigger asChild>
              <Button variant="outline" className="w-full justify-between">
                <span className="flex items-center gap-2">
                  <Settings className="h-4 w-4" />
                  Gerenciamento de Sessão
                </span>
                <span className="text-xs text-muted-foreground">
                  {config.sessionManagement.enabled ? 'Habilitado' : 'Desabilitado'}
                </span>
              </Button>
            </CollapsibleTrigger>
            <CollapsibleContent className="space-y-4 pt-2">
              <div className="space-y-2">
                <div className="flex items-center space-x-2">
                  <Checkbox
                    id="sessionEnabled"
                    checked={config.sessionManagement.enabled}
                    onCheckedChange={(checked) => handleSessionManagementChange('enabled', checked)}
                  />
                  <label
                    htmlFor="sessionEnabled"
                    className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
                  >
                    Habilitar gerenciamento de sessão
                  </label>
                </div>
                <p className="text-xs text-muted-foreground">Permite continuidade de conversas via webhook</p>
              </div>

              <div className="grid grid-cols-2 gap-4">
                <div className="space-y-2">
                  <label htmlFor="maxStoredSessions" className="text-sm font-medium">Máx. Sessões:</label>
                  <Input
                    id="maxStoredSessions"
                    type="number"
                    min="1"
                    max="1000"
                    value={config.sessionManagement.maxStoredSessions}
                    onChange={(e) => handleSessionManagementChange('maxStoredSessions', parseInt(e.target.value))}
                    disabled={!config.sessionManagement.enabled}
                  />
                </div>

                <div className="space-y-2">
                  <label htmlFor="autoCleanupDays" className="text-sm font-medium">Limpeza (dias):</label>
                  <Input
                    id="autoCleanupDays"
                    type="number"
                    min="1"
                    max="365"
                    value={config.sessionManagement.autoCleanupDays}
                    onChange={(e) => handleSessionManagementChange('autoCleanupDays', parseInt(e.target.value))}
                    disabled={!config.sessionManagement.enabled}
                  />
                </div>
              </div>

              <div className="space-y-2">
                <div className="flex items-center space-x-2">
                  <Checkbox
                    id="enableDetailedLogging"
                    checked={config.sessionManagement.enableDetailedLogging}
                    onCheckedChange={(checked) => handleSessionManagementChange('enableDetailedLogging', checked)}
                    disabled={!config.sessionManagement.enabled}
                  />
                  <label
                    htmlFor="enableDetailedLogging"
                    className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
                  >
                    Logs detalhados de sessão
                  </label>
                </div>
                <p className="text-xs text-muted-foreground">Mostra informações detalhadas sobre sessões no console</p>
              </div>

              <div className="space-y-2">
                <div className="flex items-center space-x-2">
                  <Checkbox
                    id="compressionEnabled"
                    checked={config.sessionManagement.compressionEnabled}
                    onCheckedChange={(checked) => handleSessionManagementChange('compressionEnabled', checked)}
                    disabled={!config.sessionManagement.enabled}
                  />
                  <label
                    htmlFor="compressionEnabled"
                    className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
                  >
                    Compressão de dados
                  </label>
                </div>
                <p className="text-xs text-muted-foreground">Reduz o espaço usado no armazenamento local</p>
              </div>
            </CollapsibleContent>
          </Collapsible>

          <div className="space-y-3">
            <Button
              variant="outline"
              onClick={testarConexao}
              disabled={testando || (config.tipoConexao === 'api' && !config.apiUrl.trim()) || (config.tipoConexao === 'webhook' && !config.webhookUrl.trim())}
              className="gap-2"
            >
              {testando ? (
                <>
                  <Loader2 className="h-4 w-4 animate-spin" />
                  Testando...
                </>
              ) : (
                <>
                  <TestTube className="h-4 w-4" />
                  Testar Conexão
                </>
              )}
            </Button>

            {statusTeste !== 'idle' && (
              <div className={`px-3 py-2 rounded-md text-sm font-medium ${
                statusTeste === 'success'
                  ? 'bg-green-100 text-green-800 border border-green-200'
                  : 'bg-destructive/10 text-destructive border border-destructive/20'
              }`}>
                <Badge variant={statusTeste === 'success' ? 'default' : 'destructive'} className="mr-2">
                  {statusTeste === 'success' ? 'Sucesso' : 'Erro'}
                </Badge>
                {mensagemTeste}
              </div>
            )}
          </div>
        </CardContent>

        <div className="p-4 bg-muted/50 border-t flex justify-between items-center">
          <Button
            variant="destructive"
            size="sm"
            onClick={resetarPadrao}
            className="gap-2"
          >
            <RotateCcw className="h-4 w-4" />
            Resetar Padrão
          </Button>
          <div className="flex gap-3">
            <Button
              variant="secondary"
              onClick={onClose}
            >
              Cancelar
            </Button>
            <Button
              onClick={handleSave}
              className="gap-2"
            >
              <Save className="h-4 w-4" />
              Salvar
            </Button>
          </div>
        </div>
      </Card>
    </div>
  );
};

export default ConfiguracoesChatbot;
