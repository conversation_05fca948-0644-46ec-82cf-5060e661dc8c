import React, { useState, useEffect, useRef } from 'react';
import { ArrowLeft, Eye, StopCircle, RefreshCw, X, Download, Clock, MessageSquare } from 'lucide-react';
import { useChatbotUnificado } from '../hooks/useChatbotUnificado';
import type { SessaoChat, Mensagem } from '../hooks/useChatbotAPI';
import type { ConfiguracaoUnificada } from '../hooks/useChatbotUnificado';
import { sessionManager, type StoredMessage } from '../lib/SessionManager';
import { Button } from '@/src/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/src/components/ui/card';
import { Badge } from '@/src/components/ui/badge';

interface HistoricoSessoesProps {
  onClose: () => void;
  onCarregarSessao: (sessaoId: string, mensagens: Mensagem[]) => void;
  configuracao: ConfiguracaoUnificada;
}

// Interface para sessões webhook adaptadas para o componente
interface SessaoWebhook {
  id: string;
  titulo: string;
  status: 'ativa' | 'finalizada';
  criada_em: string;
  atualizada_em: string;
  messageCount: number;
  duration?: string;
}

const HistoricoSessoes: React.FC<HistoricoSessoesProps> = ({
  onClose,
  onCarregarSessao,
  configuracao
}) => {
  const [sessoes, setSessoes] = useState<SessaoChat[]>([]);
  const [sessoesWebhook, setSessoesWebhook] = useState<SessaoWebhook[]>([]);
  const [sessaoSelecionada, setSessaoSelecionada] = useState<string | null>(null);
  const [mensagensSessao, setMensagensSessao] = useState<Mensagem[]>([]);
  const [visualizandoDetalhes, setVisualizandoDetalhes] = useState(false);
  const [carregandoWebhook, setCarregandoWebhook] = useState(false);
  const [erroWebhook, setErroWebhook] = useState<string | null>(null);

  // Ref para o container de mensagens para scroll automático
  const mensagensContainerRef = useRef<HTMLDivElement>(null);

  const {
    listarSessoes,
    obterHistorico,
    finalizarSessao,
    carregando,
    erro,
    recursos
  } = useChatbotUnificado(configuracao);

  // Detectar se está usando webhook
  const isWebhookConnection = configuracao.tipoConexao === 'webhook';

  useEffect(() => {
    if (isWebhookConnection) {
      // Para conexões webhook, carregar sessões do SessionManager
      carregarSessoesWebhook();
    } else if (recursos.listarSessoes) {
      // Para conexões API, usar o método tradicional
      carregarSessoes();
    }
  }, [recursos.listarSessoes, isWebhookConnection]);

  // Scroll automático para o container de mensagens quando a tela de detalhes é aberta
  useEffect(() => {
    if (visualizandoDetalhes && mensagensSessao.length > 0 && mensagensContainerRef.current) {
      // Pequeno delay para garantir que o DOM foi renderizado
      setTimeout(() => {
        if (mensagensContainerRef.current) {
          mensagensContainerRef.current.scrollTop = mensagensContainerRef.current.scrollHeight;
        }
      }, 100);
    }
  }, [visualizandoDetalhes, mensagensSessao]);

  const carregarSessoes = async () => {
    try {
      const sessoesCarregadas = await listarSessoes(configuracao.usuarioId, 50);
      setSessoes(sessoesCarregadas);
    } catch (error) {
      console.error('Erro ao carregar sessões:', error);
    }
  };

  const carregarSessoesWebhook = async () => {
    setCarregandoWebhook(true);
    setErroWebhook(null);

    try {
      // Obter todas as sessões do SessionManager
      const metadataList = sessionManager.getAllSessionsMetadata();

      // Converter para o formato esperado pelo componente
      const sessoesAdaptadas: SessaoWebhook[] = metadataList
        .filter(metadata => metadata.userId === configuracao.usuarioId)
        .map(metadata => {
          // Calcular duração da sessão
          const createdAt = new Date(metadata.createdAt);
          const lastActivity = new Date(metadata.lastActivity);
          const durationMs = lastActivity.getTime() - createdAt.getTime();
          const durationMinutes = Math.floor(durationMs / (1000 * 60));

          // Para o título, vamos usar um padrão baseado na data por enquanto
          // TODO: Implementar busca de primeira mensagem quando tivermos o sessionId correto
          let titulo = 'Conversa via Webhook';
          const dataFormatada = new Date(metadata.createdAt).toLocaleDateString('pt-BR', {
            day: '2-digit',
            month: '2-digit',
            hour: '2-digit',
            minute: '2-digit'
          });
          titulo = `Conversa ${dataFormatada}`;

          return {
            id: `webhook_${metadata.userId}_${new Date(metadata.createdAt).getTime()}`,
            titulo,
            status: (metadata.status === 'active' ? 'ativa' : 'finalizada') as 'ativa' | 'finalizada',
            criada_em: metadata.createdAt,
            atualizada_em: metadata.lastActivity,
            messageCount: metadata.messageCount,
            duration: durationMinutes > 0 ? `${durationMinutes} min` : '< 1 min'
          };
        })
        .sort((a, b) => new Date(b.atualizada_em).getTime() - new Date(a.atualizada_em).getTime());

      setSessoesWebhook(sessoesAdaptadas);
    } catch (error) {
      console.error('Erro ao carregar sessões webhook:', error);
      setErroWebhook('Erro ao carregar histórico de sessões webhook');
    } finally {
      setCarregandoWebhook(false);
    }
  };

  const visualizarDetalhes = async (sessaoId: string) => {
    try {
      if (isWebhookConnection) {
        // Para sessões webhook, carregar do SessionManager
        await visualizarDetalhesWebhook(sessaoId);
      } else {
        // Para sessões API, usar método tradicional
        const mensagens = await obterHistorico(sessaoId);
        setMensagensSessao(mensagens);
        setSessaoSelecionada(sessaoId);
        setVisualizandoDetalhes(true);
      }
    } catch (error) {
      console.error('Erro ao carregar detalhes da sessão:', error);
    }
  };

  const visualizarDetalhesWebhook = async (sessaoId: string) => {
    try {
      // Extrair timestamp do ID da sessão
      const timestampMatch = sessaoId.match(/webhook_.*_(\d+)$/);
      if (!timestampMatch) {
        console.error('Formato de ID de sessão webhook inválido:', sessaoId);
        return;
      }

      const timestamp = parseInt(timestampMatch[1]);

      // Encontrar a sessão correspondente nos metadados
      const metadataList = sessionManager.getAllSessionsMetadata();
      const sessionMetadata = metadataList.find(meta =>
        meta.userId === configuracao.usuarioId &&
        Math.abs(new Date(meta.createdAt).getTime() - timestamp) < 1000 // Tolerância de 1 segundo
      );

      if (!sessionMetadata) {
        console.error('Metadados da sessão webhook não encontrados:', sessaoId);
        // Criar mensagens vazias para mostrar pelo menos os metadados
        setMensagensSessao([]);
        setSessaoSelecionada(sessaoId);
        setVisualizandoDetalhes(true);
        return;
      }

      // Por enquanto, vamos mostrar mensagens vazias já que não temos acesso direto aos dados
      // TODO: Implementar acesso direto aos dados da sessão quando o SessionManager for melhorado
      const mensagensPlaceholder: Mensagem[] = [];

      // Se temos contagem de mensagens, criar placeholders
      for (let i = 0; i < sessionMetadata.messageCount; i++) {
        if (i % 2 === 0) {
          mensagensPlaceholder.push({
            id: `placeholder_user_${i}`,
            tipo: 'user',
            conteudo: 'Mensagem do usuário (dados não disponíveis)',
            timestamp: new Date(new Date(sessionMetadata.createdAt).getTime() + i * 60000).toISOString()
          });
        } else {
          mensagensPlaceholder.push({
            id: `placeholder_assistant_${i}`,
            tipo: 'assistant',
            conteudo: 'Resposta do assistente (dados não disponíveis)',
            timestamp: new Date(new Date(sessionMetadata.createdAt).getTime() + i * 60000).toISOString()
          });
        }
      }

      setMensagensSessao(mensagensPlaceholder);
      setSessaoSelecionada(sessaoId);
      setVisualizandoDetalhes(true);
    } catch (error) {
      console.error('Erro ao carregar detalhes da sessão webhook:', error);
    }
  };

  const carregarSessaoNoChat = () => {
    if (sessaoSelecionada && mensagensSessao.length > 0) {
      onCarregarSessao(sessaoSelecionada, mensagensSessao);
      onClose();
    }
  };

  const handleFinalizarSessao = async (sessaoId: string) => {
    if (confirm('Tem certeza que deseja finalizar esta sessão?')) {
      try {
        await finalizarSessao(sessaoId);
        await carregarSessoes(); // Recarregar lista
      } catch (error) {
        console.error('Erro ao finalizar sessão:', error);
      }
    }
  };

  const handleFinalizarSessaoWebhook = async (sessaoId: string) => {
    if (confirm('Tem certeza que deseja finalizar esta sessão webhook?')) {
      try {
        // Extrair timestamp do ID da sessão
        const timestampMatch = sessaoId.match(/webhook_.*_(\d+)$/);
        if (!timestampMatch) {
          console.error('Formato de ID de sessão webhook inválido:', sessaoId);
          return;
        }

        const timestamp = parseInt(timestampMatch[1]);

        // Encontrar a sessão correspondente nos metadados
        const metadataList = sessionManager.getAllSessionsMetadata();
        const sessionMetadata = metadataList.find(meta =>
          meta.userId === configuracao.usuarioId &&
          Math.abs(new Date(meta.createdAt).getTime() - timestamp) < 1000 // Tolerância de 1 segundo
        );

        if (sessionMetadata) {
          // Por enquanto, vamos apenas marcar como finalizada localmente
          // TODO: Implementar finalização real quando tivermos acesso ao sessionId correto
          console.log('Finalizando sessão webhook:', sessionMetadata);

          // Simular finalização atualizando o status nos metadados
          // Nota: Isso é uma implementação temporária
        }

        await carregarSessoesWebhook(); // Recarregar lista
      } catch (error) {
        console.error('Erro ao finalizar sessão webhook:', error);
      }
    }
  };

  const formatarData = (dataISO: string) => {
    const data = new Date(dataISO);
    return data.toLocaleString('pt-BR', {
      day: '2-digit',
      month: '2-digit',
      year: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  const obterPreviewMensagem = (titulo: string) => {
    if (titulo && titulo !== 'Nova Conversa') {
      return titulo.length > 50 ? `${titulo.substring(0, 50)}...` : titulo;
    }
    return 'Conversa sem título';
  };

  if (visualizandoDetalhes) {
    const sessao = isWebhookConnection
      ? sessoesWebhook.find(s => s.id === sessaoSelecionada)
      : sessoes.find(s => s.id === sessaoSelecionada);

    return (
      <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50 backdrop-blur-sm">
        <Card className="w-[90%] max-w-2xl max-h-[90vh] overflow-hidden shadow-2xl animate-in slide-in-from-bottom-4 duration-300 flex flex-col">
          <CardHeader className="pb-3">
            <div className="flex items-center justify-between">
              <Button
                variant="ghost"
                size="sm"
                onClick={() => setVisualizandoDetalhes(false)}
                className="gap-2"
              >
                <ArrowLeft className="h-4 w-4" />
                Voltar
              </Button>
              <CardTitle className="text-lg">Detalhes da Sessão</CardTitle>
              <Button
                variant="ghost"
                size="icon"
                onClick={onClose}
                className="h-8 w-8"
              >
                <X className="h-4 w-4" />
              </Button>
            </div>
          </CardHeader>

          <CardContent className="space-y-4">
            <div className="bg-muted p-4 rounded-lg space-y-3">
              <div className="flex justify-between items-center">
                <span className="text-sm font-medium">ID:</span>
                <Badge variant="secondary">{sessao?.id.slice(0, 8)}...</Badge>
              </div>
              <div className="flex justify-between items-center">
                <span className="text-sm font-medium">Status:</span>
                <Badge variant={sessao?.status === 'ativa' ? 'default' : 'secondary'}>
                  {sessao?.status === 'ativa' ? 'Ativa' : 'Finalizada'}
                </Badge>
              </div>
              {isWebhookConnection && 'messageCount' in sessao! && (
                <>
                  <div className="flex justify-between items-center">
                    <span className="text-sm font-medium">Tipo:</span>
                    <Badge variant="outline" className="gap-1">
                      🔗 Webhook
                    </Badge>
                  </div>
                  <div className="flex justify-between items-center">
                    <span className="text-sm font-medium">Mensagens:</span>
                    <Badge variant="secondary" className="gap-1">
                      <MessageSquare className="h-3 w-3" />
                      {sessao.messageCount}
                    </Badge>
                  </div>
                  <div className="flex justify-between items-center">
                    <span className="text-sm font-medium">Duração:</span>
                    <Badge variant="secondary" className="gap-1">
                      <Clock className="h-3 w-3" />
                      {sessao.duration}
                    </Badge>
                  </div>
                </>
              )}
              <div className="flex justify-between items-center">
                <span className="text-sm font-medium">Criada em:</span>
                <span className="text-sm text-muted-foreground">{sessao && formatarData(sessao.criada_em)}</span>
              </div>
              <div className="flex justify-between items-center">
                <span className="text-sm font-medium">Atualizada em:</span>
                <span className="text-sm text-muted-foreground">{sessao && formatarData(sessao.atualizada_em)}</span>
              </div>
            </div>

            <div className="space-y-3">
              <h4 className="text-base font-medium">Mensagens ({mensagensSessao.length})</h4>
              <div
                ref={mensagensContainerRef}
                className="max-h-80 overflow-y-auto border rounded-lg scroll-smooth"
              >
                {mensagensSessao.map((msg) => (
                  <div key={msg.id} className={`p-3 border-b last:border-b-0 ${
                    msg.tipo === 'user' ? 'bg-muted/50' : 'bg-background'
                  }`}>
                    <div className="flex justify-between items-center mb-2">
                      <Badge variant={msg.tipo === 'user' ? 'default' : 'secondary'} className="text-xs">
                        {msg.tipo === 'user' ? 'Você' : 'Assistente'}
                      </Badge>
                      <span className="text-xs text-muted-foreground">
                        {formatarData(msg.timestamp)}
                      </span>
                    </div>
                    <div className="text-sm leading-relaxed">
                      {msg.conteudo.length > 100
                        ? `${msg.conteudo.substring(0, 100)}...`
                        : msg.conteudo
                      }
                    </div>
                  </div>
                ))}
              </div>
            </div>

            <div className="flex justify-center pt-4 border-t">
              <Button
                onClick={carregarSessaoNoChat}
                disabled={!mensagensSessao.length}
                className="gap-2"
              >
                <Download className="h-4 w-4" />
                Carregar no Chat
              </Button>
            </div>
          </CardContent>
        </Card>
      </div>
    );
  }

  return (
    <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50 backdrop-blur-sm">
      <Card className="w-[90%] max-w-xl max-h-[90vh] overflow-hidden shadow-2xl animate-in slide-in-from-bottom-4 duration-300 flex flex-col">
        <CardHeader className="pb-3">
          <div className="flex items-center justify-between">
            <CardTitle className="text-lg">Histórico de Conversas</CardTitle>
            <Button
              variant="ghost"
              size="icon"
              onClick={onClose}
              className="h-8 w-8"
            >
              <X className="h-4 w-4" />
            </Button>
          </div>
        </CardHeader>

        <CardContent className="flex-1 overflow-y-auto">
          {isWebhookConnection && carregandoWebhook && (
            <div className="flex flex-col items-center justify-center py-10 text-muted-foreground">
              <RefreshCw className="h-8 w-8 mb-4 animate-spin" />
              <p>Carregando sessões webhook...</p>
            </div>
          )}

          {isWebhookConnection && erroWebhook && (
            <div className="bg-destructive/10 text-destructive p-4 rounded-lg text-center border border-destructive/20">
              <p className="mb-3">{erroWebhook}</p>
              <Button
                variant="destructive"
                size="sm"
                onClick={carregarSessoesWebhook}
                className="gap-2"
              >
                <RefreshCw className="h-4 w-4" />
                Tentar Novamente
              </Button>
            </div>
          )}

          {!isWebhookConnection && !recursos.listarSessoes && (
            <div className="text-center py-15 text-muted-foreground">
              <div className="text-4xl mb-4">🔗</div>
              <h4 className="mb-2 font-medium">Histórico não disponível</h4>
              <p className="text-sm">O histórico de sessões não está disponível para este tipo de conexão.</p>
              <p className="text-sm mt-2">Verifique a configuração da conexão.</p>
            </div>
          )}

          {!isWebhookConnection && recursos.listarSessoes && carregando && (
            <div className="flex flex-col items-center justify-center py-10 text-muted-foreground">
              <RefreshCw className="h-8 w-8 mb-4 animate-spin" />
              <p>Carregando sessões...</p>
            </div>
          )}

          {!isWebhookConnection && recursos.listarSessoes && erro && (
            <div className="bg-destructive/10 text-destructive p-4 rounded-lg text-center border border-destructive/20">
              <p className="mb-3">{erro}</p>
              <Button
                variant="destructive"
                size="sm"
                onClick={carregarSessoes}
                className="gap-2"
              >
                <RefreshCw className="h-4 w-4" />
                Tentar Novamente
              </Button>
            </div>
          )}

          {/* Seção para conexões API tradicionais */}
          {!isWebhookConnection && recursos.listarSessoes && !carregando && !erro && sessoes.length === 0 && (
            <div className="text-center py-15 text-muted-foreground">
              <div className="text-4xl mb-4">💬</div>
              <h4 className="mb-2 font-medium">Nenhuma conversa encontrada</h4>
              <p className="text-sm">Suas conversas com o assistente SOGE aparecerão aqui.</p>
            </div>
          )}

          {!isWebhookConnection && recursos.listarSessoes && !carregando && sessoes.length > 0 && (
            <div className="space-y-3">
              {sessoes.map((sessao) => (
                <Card key={sessao.id} className="transition-all duration-200 hover:shadow-md">
                  <CardContent className="p-4">
                    <div className="flex justify-between items-center">
                      <div className="flex-1 min-w-0">
                        <div className="font-medium mb-1.5 overflow-hidden text-ellipsis whitespace-nowrap">
                          {obterPreviewMensagem(sessao.titulo)}
                        </div>
                        <div className="flex items-center gap-3 text-sm text-muted-foreground">
                          <Badge variant={sessao.status === 'ativa' ? 'default' : 'secondary'} className="text-xs">
                            {sessao.status === 'ativa' ? 'Ativa' : 'Finalizada'}
                          </Badge>
                          <span className="text-xs">
                            {formatarData(sessao.atualizada_em)}
                          </span>
                        </div>
                      </div>

                      <div className="flex gap-2">
                        <Button
                          variant="ghost"
                          size="icon"
                          onClick={() => visualizarDetalhes(sessao.id)}
                          className="h-8 w-8"
                          title="Ver detalhes"
                        >
                          <Eye className="h-4 w-4" />
                        </Button>

                        {sessao.status === 'ativa' && (
                          <Button
                            variant="ghost"
                            size="icon"
                            onClick={() => handleFinalizarSessao(sessao.id)}
                            className="h-8 w-8 text-destructive hover:text-destructive"
                            title="Finalizar sessão"
                          >
                            <StopCircle className="h-4 w-4" />
                          </Button>
                        )}
                      </div>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          )}

          {/* Seção para conexões webhook */}
          {isWebhookConnection && !carregandoWebhook && !erroWebhook && sessoesWebhook.length === 0 && (
            <div className="text-center py-15 text-muted-foreground">
              <div className="text-4xl mb-4">🔗</div>
              <h4 className="mb-2 font-medium">Nenhuma sessão webhook encontrada</h4>
              <p className="text-sm">Suas conversas via webhook N8N aparecerão aqui.</p>
              <p className="text-sm mt-2">Inicie uma conversa para ver o histórico.</p>
            </div>
          )}

          {isWebhookConnection && !carregandoWebhook && sessoesWebhook.length > 0 && (
            <div className="space-y-3">
              {sessoesWebhook.map((sessao) => (
                <Card key={sessao.id} className="transition-all duration-200 hover:shadow-md">
                  <CardContent className="p-4">
                    <div className="flex justify-between items-center">
                      <div className="flex-1 min-w-0">
                        <div className="font-medium mb-1.5 overflow-hidden text-ellipsis whitespace-nowrap">
                          {obterPreviewMensagem(sessao.titulo)}
                        </div>
                        <div className="flex items-center gap-3 text-sm text-muted-foreground">
                          <Badge variant="outline" className="text-xs gap-1">
                            🔗 Webhook
                          </Badge>
                          <Badge variant={sessao.status === 'ativa' ? 'default' : 'secondary'} className="text-xs">
                            {sessao.status === 'ativa' ? 'Ativa' : 'Finalizada'}
                          </Badge>
                          <Badge variant="secondary" className="text-xs gap-1">
                            <MessageSquare className="h-3 w-3" />
                            {sessao.messageCount}
                          </Badge>
                          <span className="text-xs">
                            {formatarData(sessao.atualizada_em)}
                          </span>
                        </div>
                      </div>

                      <div className="flex gap-2">
                        <Button
                          variant="ghost"
                          size="icon"
                          onClick={() => visualizarDetalhes(sessao.id)}
                          className="h-8 w-8"
                          title="Ver detalhes"
                        >
                          <Eye className="h-4 w-4" />
                        </Button>

                        {sessao.status === 'ativa' && (
                          <Button
                            variant="ghost"
                            size="icon"
                            onClick={() => handleFinalizarSessaoWebhook(sessao.id)}
                            className="h-8 w-8 text-destructive hover:text-destructive"
                            title="Finalizar sessão"
                          >
                            <StopCircle className="h-4 w-4" />
                          </Button>
                        )}
                      </div>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          )}
        </CardContent>

        <div className="p-4 bg-muted/50 border-t flex justify-between items-center">
          <Button
            variant="outline"
            size="sm"
            onClick={isWebhookConnection ? carregarSessoesWebhook : carregarSessoes}
            className="gap-2"
          >
            <RefreshCw className="h-4 w-4" />
            Atualizar
          </Button>
          <div className="text-sm text-muted-foreground">
            {isWebhookConnection ? (
              <>
                Total: {sessoesWebhook.length} sessões webhook
                {sessoesWebhook.length > 0 && (
                  <span className="ml-2">
                    • {sessoesWebhook.reduce((acc, s) => acc + s.messageCount, 0)} mensagens
                  </span>
                )}
              </>
            ) : (
              `Total: ${sessoes.length} sessões`
            )}
          </div>
        </div>
      </Card>
    </div>
  );
};

export default HistoricoSessoes;
