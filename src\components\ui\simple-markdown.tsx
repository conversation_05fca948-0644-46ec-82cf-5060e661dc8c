import React from 'react';
import { cn } from "@/src/lib/utils";

interface SimpleMarkdownProps {
  children: string;
  className?: string;
}

export const SimpleMarkdown: React.FC<SimpleMarkdownProps> = ({ children, className }) => {
  const parseMarkdown = (text: string) => {
    const lines = text.split('\n');
    const elements: React.ReactNode[] = [];
    let currentIndex = 0;

    for (let i = 0; i < lines.length; i++) {
      const line = lines[i];
      
      // Lista com -
      if (line.trim().startsWith('- ')) {
        const listItems: string[] = [];
        let j = i;
        
        while (j < lines.length && lines[j].trim().startsWith('- ')) {
          listItems.push(lines[j].trim().substring(2));
          j++;
        }
        
        elements.push(
          <ul key={currentIndex++} className="ml-4 list-disc space-y-1 my-2">
            {listItems.map((item, idx) => (
              <li key={idx} className="text-sm">
                {parseInlineMarkdown(item)}
              </li>
            ))}
          </ul>
        );
        
        i = j - 1;
        continue;
      }
      
      // Bloco de código
      if (line.trim().startsWith('```')) {
        const codeLines: string[] = [];
        let j = i + 1;
        
        while (j < lines.length && !lines[j].trim().startsWith('```')) {
          codeLines.push(lines[j]);
          j++;
        }
        
        if (j < lines.length) {
          elements.push(
            <pre key={currentIndex++} className="bg-gray-100 dark:bg-gray-800 rounded p-3 my-2 overflow-x-auto">
              <code className="text-xs font-mono">
                {codeLines.join('\n')}
              </code>
            </pre>
          );
          i = j;
          continue;
        }
      }
      
      // Linha normal
      if (line.trim()) {
        elements.push(
          <p key={currentIndex++} className="text-sm leading-relaxed my-1">
            {parseInlineMarkdown(line)}
          </p>
        );
      } else {
        // Linha vazia - adiciona espaço
        elements.push(<br key={currentIndex++} />);
      }
    }

    return elements;
  };

  const parseInlineMarkdown = (text: string): React.ReactNode => {
    const parts: React.ReactNode[] = [];
    let currentText = text;
    let keyIndex = 0;

    // Parse **negrito**
    currentText = currentText.replace(/\*\*(.*?)\*\*/g, (_, content) => {
      const placeholder = `__BOLD_${keyIndex}__`;
      parts.push(
        <strong key={`bold-${keyIndex}`} className="font-semibold">
          {content}
        </strong>
      );
      keyIndex++;
      return placeholder;
    });

    // Parse *itálico*
    currentText = currentText.replace(/\*(.*?)\*/g, (_, content) => {
      const placeholder = `__ITALIC_${keyIndex}__`;
      parts.push(
        <em key={`italic-${keyIndex}`} className="italic">
          {content}
        </em>
      );
      keyIndex++;
      return placeholder;
    });

    // Parse `código inline`
    currentText = currentText.replace(/`(.*?)`/g, (_, content) => {
      const placeholder = `__CODE_${keyIndex}__`;
      parts.push(
        <code key={`code-${keyIndex}`} className="bg-gray-100 dark:bg-gray-800 px-1 py-0.5 rounded text-xs font-mono">
          {content}
        </code>
      );
      keyIndex++;
      return placeholder;
    });

    // Dividir o texto e inserir os componentes
    const textParts = currentText.split(/(__(?:BOLD|ITALIC|CODE)_\d+__)/);
    const result: React.ReactNode[] = [];

    textParts.forEach((part) => {
      if (part.startsWith('__BOLD_')) {
        const boldIndex = parseInt(part.match(/__BOLD_(\d+)__/)?.[1] || '0');
        result.push(parts.find((p: any) => p.key === `bold-${boldIndex}`));
      } else if (part.startsWith('__ITALIC_')) {
        const italicIndex = parseInt(part.match(/__ITALIC_(\d+)__/)?.[1] || '0');
        result.push(parts.find((p: any) => p.key === `italic-${italicIndex}`));
      } else if (part.startsWith('__CODE_')) {
        const codeIndex = parseInt(part.match(/__CODE_(\d+)__/)?.[1] || '0');
        result.push(parts.find((p: any) => p.key === `code-${codeIndex}`));
      } else if (part) {
        result.push(part);
      }
    });

    return result.length > 0 ? result : text;
  };

  return (
    <div className={cn('space-y-1', className)}>
      {parseMarkdown(children)}
    </div>
  );
};
