import type { ConfiguracaoAPI } from '../hooks/useChatbotAPI';
import type { SessionStorageConfig } from '../lib/SessionStorage';

/**
 * Configuração estendida com gerenciamento de sessões
 */
export interface ConfiguracaoExtendida extends ConfiguracaoAPI {
  sessionManagement: SessionStorageConfig;
}

/**
 * Configuração padrão do chatbot SOGE
 */
export const configuracaoPadrao: ConfiguracaoExtendida = {
  // URL da API do chatbot SOGE
  apiUrl: 'http://localhost:8000',
  
  // Timeout para requisições em milissegundos (30 segundos)
  timeout: 30000,
  
  // Número máximo de tentativas em caso de erro
  maxTentativas: 3,
  
  // Habilitar logs de debug no console
  habilitarLogs: false,
  
  // ID único do usuário (gerado automaticamente)
  usuarioId: `user_${Date.now()}`,

  // Configurações de gerenciamento de sessões
  sessionManagement: {
    maxStoredSessions: 100,
    autoCleanupDays: 30,
    enableDetailedLogging: false,
    compressionEnabled: false
  }
};

/**
 * Configuração para ambiente de desenvolvimento
 */
export const configuracaoDesenvolvimento: ConfiguracaoExtendida = {
  ...configuracaoPadrao,
  habilitarLogs: true,
  timeout: 10000, // Timeout menor para desenvolvimento
  sessionManagement: {
    ...configuracaoPadrao.sessionManagement,
    enableDetailedLogging: true
  }
};

/**
 * Configuração para ambiente de produção
 */
export const configuracaoProducao: ConfiguracaoExtendida = {
  ...configuracaoPadrao,
  apiUrl: 'https://api.fibraepsilon.com',
  timeout: 60000, // Timeout maior para produção
  maxTentativas: 5,
  sessionManagement: {
    ...configuracaoPadrao.sessionManagement,
    maxStoredSessions: 200,
    autoCleanupDays: 60
  }
};

/**
 * Obter configuração baseada no ambiente
 */
export const obterConfiguracaoAmbiente = (): ConfiguracaoExtendida => {
  const ambiente = process.env.NODE_ENV || 'development';
  
  switch (ambiente) {
    case 'production':
      return configuracaoProducao;
    case 'development':
      return configuracaoDesenvolvimento;
    default:
      return configuracaoPadrao;
  }
};

/**
 * Validar configuração básica
 */
export const validarConfiguracao = (config: ConfiguracaoAPI): string[] => {
  const erros: string[] = [];
  
  if (!config.apiUrl || !config.apiUrl.trim()) {
    erros.push('URL da API é obrigatória');
  }
  
  if (!config.apiUrl.startsWith('http://') && !config.apiUrl.startsWith('https://')) {
    erros.push('URL da API deve começar com http:// ou https://');
  }
  
  if (config.timeout < 1000) {
    erros.push('Timeout deve ser pelo menos 1000ms');
  }
  
  if (config.timeout > 300000) {
    erros.push('Timeout não deve exceder 300000ms (5 minutos)');
  }
  
  if (config.maxTentativas < 1) {
    erros.push('Máximo de tentativas deve ser pelo menos 1');
  }
  
  if (config.maxTentativas > 10) {
    erros.push('Máximo de tentativas não deve exceder 10');
  }
  
  if (!config.usuarioId || !config.usuarioId.trim()) {
    erros.push('ID do usuário é obrigatório');
  }
  
  return erros;
};

/**
 * Validar configuração estendida com gerenciamento de sessões
 */
export const validarConfiguracaoExtendida = (config: ConfiguracaoExtendida): string[] => {
  const erros = validarConfiguracao(config);
  
  if (!config.sessionManagement) {
    erros.push('Configuração de gerenciamento de sessões é obrigatória');
    return erros;
  }
  
  const sessionConfig = config.sessionManagement;
  
  if (sessionConfig.maxStoredSessions < 1) {
    erros.push('Máximo de sessões armazenadas deve ser pelo menos 1');
  }
  
  if (sessionConfig.maxStoredSessions > 1000) {
    erros.push('Máximo de sessões armazenadas não deve exceder 1000');
  }
  
  if (sessionConfig.autoCleanupDays < 1) {
    erros.push('Dias para limpeza automática deve ser pelo menos 1');
  }
  
  if (sessionConfig.autoCleanupDays > 365) {
    erros.push('Dias para limpeza automática não deve exceder 365');
  }
  
  return erros;
};

/**
 * Salvar configuração básica no localStorage
 */
export const salvarConfiguracao = (config: ConfiguracaoAPI): void => {
  try {
    localStorage.setItem('chatbot-config', JSON.stringify(config));
  } catch (error) {
    console.error('Erro ao salvar configuração:', error);
  }
};

/**
 * Salvar configuração estendida no localStorage
 */
export const salvarConfiguracaoExtendida = (config: ConfiguracaoExtendida): void => {
  try {
    localStorage.setItem('chatbot-config-extended', JSON.stringify(config));
  } catch (error) {
    console.error('Erro ao salvar configuração estendida:', error);
  }
};

/**
 * Carregar configuração básica do localStorage
 */
export const carregarConfiguracao = (): ConfiguracaoAPI => {
  try {
    const configSalva = localStorage.getItem('chatbot-config');
    if (configSalva) {
      const config = JSON.parse(configSalva);
      
      // Validar configuração carregada
      const erros = validarConfiguracao(config);
      if (erros.length === 0) {
        return config;
      } else {
        console.warn('Configuração inválida encontrada, usando padrão:', erros);
      }
    }
  } catch (error) {
    console.error('Erro ao carregar configuração:', error);
  }
  
  // Retornar configuração padrão se não conseguir carregar
  return obterConfiguracaoAmbiente();
};

/**
 * Carregar configuração estendida do localStorage
 */
export const carregarConfiguracaoExtendida = (): ConfiguracaoExtendida => {
  try {
    const configSalva = localStorage.getItem('chatbot-config-extended');
    if (configSalva) {
      const config = JSON.parse(configSalva);
      
      // Validar configuração carregada
      const erros = validarConfiguracaoExtendida(config);
      if (erros.length === 0) {
        return config;
      } else {
        console.warn('Configuração estendida inválida encontrada, usando padrão:', erros);
      }
    }
  } catch (error) {
    console.error('Erro ao carregar configuração estendida:', error);
  }
  
  // Retornar configuração padrão se não conseguir carregar
  return obterConfiguracaoAmbiente();
};

/**
 * Resetar configuração básica para padrão
 */
export const resetarConfiguracao = (): ConfiguracaoAPI => {
  const configPadrao = obterConfiguracaoAmbiente();
  salvarConfiguracao(configPadrao);
  return configPadrao;
};

/**
 * Resetar configuração estendida para padrão
 */
export const resetarConfiguracaoExtendida = (): ConfiguracaoExtendida => {
  const configPadrao = obterConfiguracaoAmbiente();
  salvarConfiguracaoExtendida(configPadrao);
  return configPadrao;
};
