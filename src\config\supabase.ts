import { createClient } from '@supabase/supabase-js'

// Configurações do Supabase
const supabaseUrl = process.env.PLASMO_PUBLIC_SUPABASE_URL || 'YOUR_SUPABASE_URL'
const supabaseAnonKey = process.env.PLASMO_PUBLIC_SUPABASE_ANON_KEY || 'YOUR_SUPABASE_ANON_KEY'

// Criar cliente Supabase
export const supabase = createClient(supabaseUrl, supabaseAnonKey, {
  auth: {
    autoRefreshToken: true,
    persistSession: true,
    detectSessionInUrl: false
  }
})

// Tipos para autenticação
export interface AuthUser {
  id: string
  email: string
  name?: string
  avatar_url?: string
}

// Configuração de autenticação
export const authConfig = {
  // Provedores de autenticação habilitados
  providers: ['email', 'google'] as const,
  
  // Configurações de sessão
  session: {
    persistSession: true,
    autoRefreshToken: true
  },
  
  // URLs de redirecionamento (para extensão Chrome)
  redirectUrls: {
    signIn: chrome?.runtime?.getURL?.('popup.html') || window.location.origin,
    signOut: chrome?.runtime?.getURL?.('popup.html') || window.location.origin
  }
}

export type AuthProvider = typeof authConfig.providers[number]
