import { useState, useCallback } from 'react';

export interface Mensagem {
  id: string;
  tipo: 'user' | 'assistant';
  conteudo: string;
  timestamp: string;
}

export interface ConfiguracaoAPI {
  apiUrl: string;
  timeout: number;
  maxTentativas: number;
  habilitarLogs: boolean;
  usuarioId: string;
}

export interface SessaoChat {
  id: string;
  usuario_id: string;
  titulo: string;
  status: 'ativa' | 'finalizada';
  criada_em: string;
  atualizada_em: string;
}

export interface RespostaEnviarMensagem {
  sucesso: boolean;
  sessao_id: string;
  mensagem_usuario: {
    id: string;
    sessao_id: string;
    tipo: 'user';
    conteudo: string;
    timestamp: string;
  };
  mensagem_assistente: {
    id: string;
    sessao_id: string;
    tipo: 'assistant';
    conteudo: string;
    timestamp: string;
  };
  tempo_processamento: number;
}

export interface RespostaHistorico {
  sucesso: boolean;
  sessao: SessaoChat;
  total_mensagens: number;
  mensagens: Mensagem[];
}

export interface RespostaListarSessoes {
  sucesso: boolean;
  total: number;
  sessoes: SessaoChat[];
}

class ApiError extends Error {
  constructor(
    message: string,
    public status: number,
    public details?: any
  ) {
    super(message);
    this.name = 'ApiError';
  }
}

export const useChatbotAPI = (apiUrl: string = 'http://localhost:8000') => {
  const [carregando, setCarregando] = useState(false);
  const [erro, setErro] = useState<string | null>(null);

  const fazerRequisicao = useCallback(async <T>(
    url: string,
    options: RequestInit = {}
  ): Promise<T> => {
    const maxTentativas = 3;
    let ultimoErro: Error;

    for (let tentativa = 0; tentativa < maxTentativas; tentativa++) {
      try {
        // Criar AbortController para timeout
        const controller = new AbortController();
        const timeoutId = setTimeout(() => controller.abort(), 30000);

        const response = await fetch(url, {
          signal: controller.signal,
          headers: {
            'Content-Type': 'application/json',
            ...options.headers,
          },
          ...options,
        });

        clearTimeout(timeoutId);

        if (!response.ok) {
          const errorData = await response.json().catch(() => ({}));
          throw new ApiError(
            errorData.detail || `Erro HTTP ${response.status}`,
            response.status,
            errorData
          );
        }

        return await response.json();

      } catch (error) {
        ultimoErro = error as Error;

        // Não tentar novamente em erros 4xx (exceto 429)
        if (error instanceof ApiError && 
            error.status >= 400 && 
            error.status < 500 && 
            error.status !== 429) {
          throw error;
        }

        // Aguardar antes de tentar novamente
        if (tentativa < maxTentativas - 1) {
          await new Promise(resolve => 
            setTimeout(resolve, 1000 * (tentativa + 1))
          );
        }
      }
    }

    throw ultimoErro!;
  }, []);

  const enviarMensagem = useCallback(async (
    mensagem: string,
    sessaoId: string | null = null,
    usuarioId: string | null = null,
    contextoAdicional: Record<string, any> = {}
  ): Promise<RespostaEnviarMensagem> => {
    setCarregando(true);
    setErro(null);

    try {
      const response = await fazerRequisicao<RespostaEnviarMensagem>(
        `${apiUrl}/chat/mensagem`,
        {
          method: 'POST',
          body: JSON.stringify({
            sessao_id: sessaoId,
            mensagem,
            usuario_id: usuarioId,
            contexto_adicional: {
              timestamp: new Date().toISOString(),
              fonte: 'plasmo_extension',
              ...contextoAdicional,
            },
          }),
        }
      );

      return response;
    } catch (error) {
      const mensagemErro = error instanceof ApiError 
        ? error.message 
        : 'Erro de conexão com o servidor';
      
      setErro(mensagemErro);
      throw error;
    } finally {
      setCarregando(false);
    }
  }, [apiUrl, fazerRequisicao]);

  const obterHistorico = useCallback(async (
    sessaoId: string,
    limite: number = 50
  ): Promise<Mensagem[]> => {
    setCarregando(true);
    setErro(null);

    try {
      const response = await fazerRequisicao<RespostaHistorico>(
        `${apiUrl}/chat/sessoes/${sessaoId}/historico?limite=${limite}`
      );

      return response.mensagens;
    } catch (error) {
      const mensagemErro = error instanceof ApiError 
        ? error.message 
        : 'Erro ao carregar histórico';
      
      setErro(mensagemErro);
      throw error;
    } finally {
      setCarregando(false);
    }
  }, [apiUrl, fazerRequisicao]);

  const listarSessoes = useCallback(async (
    usuarioId: string | null = null,
    limite: number = 20
  ): Promise<SessaoChat[]> => {
    setCarregando(true);
    setErro(null);

    try {
      const params = new URLSearchParams();
      if (usuarioId) params.append('usuario_id', usuarioId);
      params.append('limite', limite.toString());

      const response = await fazerRequisicao<RespostaListarSessoes>(
        `${apiUrl}/chat/sessoes?${params}`
      );

      return response.sessoes;
    } catch (error) {
      const mensagemErro = error instanceof ApiError 
        ? error.message 
        : 'Erro ao listar sessões';
      
      setErro(mensagemErro);
      throw error;
    } finally {
      setCarregando(false);
    }
  }, [apiUrl, fazerRequisicao]);

  const finalizarSessao = useCallback(async (
    sessaoId: string
  ): Promise<void> => {
    setCarregando(true);
    setErro(null);

    try {
      await fazerRequisicao(
        `${apiUrl}/chat/sessoes/${sessaoId}/finalizar`,
        {
          method: 'POST',
        }
      );
    } catch (error) {
      const mensagemErro = error instanceof ApiError 
        ? error.message 
        : 'Erro ao finalizar sessão';
      
      setErro(mensagemErro);
      throw error;
    } finally {
      setCarregando(false);
    }
  }, [apiUrl, fazerRequisicao]);

  const limparErro = useCallback(() => {
    setErro(null);
  }, []);

  return {
    // Estados
    carregando,
    erro,

    // Métodos da API
    enviarMensagem,
    obterHistorico,
    listarSessoes,
    finalizarSessao,

    // Utilitários
    limparErro,
  };
};
