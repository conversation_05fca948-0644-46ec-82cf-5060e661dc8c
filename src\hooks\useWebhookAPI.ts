import { useState, useCallback } from 'react';
import type { RespostaEnviarMensagem } from './useChatbotAPI';
import { sessionManager, type SessionMetadata, type StoredMessage, type SessionError } from '../lib/SessionManager';

export interface ConfiguracaoWebhook {
  webhookUrl: string;
  timeout: number;
  maxTentativas: number;
  habilitarLogs: boolean;
  usuarioId: string;
}

// Enhanced webhook payload interface matching design specification
export interface WebhookPayload {
  message: string;
  session_id: string;  // Always present
  user_id: string;
  context: {
    timestamp: string;
    fonte: string;
    user_agent: string;
    message_sequence: number;
    session_metadata: SessionMetadata;
  };
}

// Enhanced webhook response interface
export interface WebhookResponse {
  response: string;
  session_id: string;  // Confirmation from N8N
  message_id: string;
  timestamp: string;
  processing_time?: number;
  status?: 'success' | 'error' | 'session_invalid';
  error_details?: {
    code: string;
    message: string;
    recoverable: boolean;
  };
}

class WebhookError extends Error {
  constructor(
    message: string,
    public status: number,
    public details?: any
  ) {
    super(message);
    this.name = 'WebhookError';
  }
}

// Session validation and error handling utilities
const validateSessionResponse = (response: WebhookResponse, expectedSessionId: string): SessionError | null => {
  // Check if response indicates session is invalid
  if (response.status === 'session_invalid') {
    return {
      type: 'invalid_session',
      sessionId: expectedSessionId,
      message: response.error_details?.message || 'Session ID not recognized by N8N',
      timestamp: new Date().toISOString(),
      recoveryAction: 'new_session'
    };
  }

  // Check if session_id in response doesn't match expected
  if (response.session_id && response.session_id !== expectedSessionId) {
    return {
      type: 'invalid_session',
      sessionId: expectedSessionId,
      message: `Session ID mismatch: expected ${expectedSessionId}, got ${response.session_id}`,
      timestamp: new Date().toISOString(),
      recoveryAction: 'new_session'
    };
  }

  // Check if response indicates N8N error
  if (response.status === 'error') {
    return {
      type: 'n8n_error',
      sessionId: expectedSessionId,
      message: response.error_details?.message || 'N8N processing error',
      timestamp: new Date().toISOString(),
      recoveryAction: response.error_details?.recoverable ? 'retry' : 'fallback'
    };
  }

  return null; // No validation errors
};

const handleSessionRecovery = async (
  sessionError: SessionError,
  userId: string,
  originalMessage: string,
  webhookUrl: string,
  fazerRequisicao: (url: string, options: RequestInit) => Promise<WebhookResponse>
): Promise<{ newSessionId: string; response: WebhookResponse } | null> => {
  try {
    switch (sessionError.recoveryAction) {
      case 'new_session':
        // Create new session and retry the message
        const newSessionId = sessionManager.createNewSession(userId);
        console.log(`[SessionRecovery] Created new session ${newSessionId} after error:`, sessionError.message);

        // Load new session data
        const newSessionData = sessionManager.loadSession(newSessionId);

        // Create payload with new session
        const recoveryPayload: WebhookPayload = {
          message: originalMessage,
          session_id: newSessionId,
          user_id: userId,
          context: {
            timestamp: new Date().toISOString(),
            fonte: 'plasmo_extension_webhook_recovery',
            user_agent: navigator.userAgent,
            message_sequence: 1,
            session_metadata: newSessionData?.metadata || {
              userId,
              createdAt: new Date().toISOString(),
              lastActivity: new Date().toISOString(),
              messageCount: 0,
              status: 'active'
            }
          }
        };

        const recoveryResponse = await fazerRequisicao(webhookUrl, {
          method: 'POST',
          body: JSON.stringify(recoveryPayload)
        });

        return { newSessionId, response: recoveryResponse };

      case 'retry':
        // Retry with same session after a brief delay
        console.log(`[SessionRecovery] Retrying with same session after error:`, sessionError.message);
        await new Promise(resolve => setTimeout(resolve, 1000));

        const sessionData = sessionManager.loadSession(sessionError.sessionId);
        if (!sessionData) {
          throw new Error('Session data not found for retry');
        }

        const retryPayload: WebhookPayload = {
          message: originalMessage,
          session_id: sessionError.sessionId,
          user_id: userId,
          context: {
            timestamp: new Date().toISOString(),
            fonte: 'plasmo_extension_webhook_retry',
            user_agent: navigator.userAgent,
            message_sequence: sessionData.metadata.messageCount + 1,
            session_metadata: sessionData.metadata
          }
        };

        const retryResponse = await fazerRequisicao(webhookUrl, {
          method: 'POST',
          body: JSON.stringify(retryPayload)
        });

        return { newSessionId: sessionError.sessionId, response: retryResponse };

      case 'fallback':
        // Log error and return null to indicate fallback needed
        console.error(`[SessionRecovery] Fallback required for session error:`, sessionError);
        return null;

      default:
        console.warn(`[SessionRecovery] Unknown recovery action:`, sessionError.recoveryAction);
        return null;
    }
  } catch (error) {
    console.error(`[SessionRecovery] Recovery attempt failed:`, error);
    return null;
  }
};

export const useWebhookAPI = (webhookUrl: string = 'https://n8n.sondtheanime.site/webhook/chat') => {
  const [carregando, setCarregando] = useState(false);
  const [erro, setErro] = useState<string | null>(null);

  const fazerRequisicaoWebhook = useCallback(async <T>(
    url: string,
    options: RequestInit = {}
  ): Promise<T> => {
    const maxTentativas = 3;
    let ultimoErro: Error;

    for (let tentativa = 0; tentativa < maxTentativas; tentativa++) {
      try {
        // Criar AbortController para timeout - increased for N8N processing
        const controller = new AbortController();
        const timeoutId = setTimeout(() => controller.abort(), 60000); // Increased to 60 seconds

        const response = await fetch(url, {
          signal: controller.signal,
          headers: {
            'Content-Type': 'application/json',
            ...options.headers,
          },
          ...options,
        });

        clearTimeout(timeoutId);

        if (!response.ok) {
          let errorData: any = {};
          let errorText = '';

          try {
            // Try to parse as JSON first
            errorData = await response.json();
          } catch {
            // If JSON parsing fails, try to get text
            try {
              errorText = await response.text();
            } catch {
              errorText = `HTTP ${response.status} ${response.statusText}`;
            }
          }

          // Create more specific error messages based on status and content
          let errorMessage = '';

          if (response.status === 404) {
            if (errorText.includes('webhook') || errorText.includes('not registered')) {
              errorMessage = `Webhook "chat" não está registrado no N8N. Verifique se o webhook está configurado corretamente no N8N.`;
            } else {
              errorMessage = `Endpoint webhook não encontrado. Verifique a URL: ${url}`;
            }
          } else if (response.status === 500) {
            errorMessage = `Erro interno do servidor N8N. O webhook pode estar com problemas de configuração.`;
          } else if (response.status === 502 || response.status === 503) {
            errorMessage = `Servidor N8N indisponível. Verifique se o N8N está rodando.`;
          } else {
            // Use error data or text, with fallback
            errorMessage = errorData.message || errorData.error || errorText || `Erro HTTP ${response.status}`;
          }

          throw new WebhookError(
            errorMessage,
            response.status,
            { errorData, errorText, url }
          );
        }

        return await response.json();

      } catch (error) {
        ultimoErro = error as Error;

        // Não tentar novamente em erros 4xx (exceto 429)
        if (error instanceof WebhookError &&
          error.status >= 400 &&
          error.status < 500 &&
          error.status !== 429) {
          throw error;
        }

        // Aguardar antes de tentar novamente
        if (tentativa < maxTentativas - 1) {
          await new Promise(resolve =>
            setTimeout(resolve, 1000 * (tentativa + 1))
          );
        }
      }
    }

    throw ultimoErro!;
  }, []);

  const enviarMensagemWebhook = useCallback(async (
    mensagem: string,
    sessaoId: string | null = null,
    usuarioId: string | null = null,
    contextoAdicional: Record<string, any> = {}
  ): Promise<RespostaEnviarMensagem> => {
    setCarregando(true);
    setErro(null);

    try {
      const finalUserId = usuarioId || `user_${Date.now()}`;

      // Get or create session using SessionManager
      let currentSessionId = sessaoId || sessionManager.getCurrentSessionId();

      if (!currentSessionId) {
        // Create new session if none exists
        currentSessionId = sessionManager.createNewSession(finalUserId);
      }

      // Load session data to get metadata and message sequence
      let sessionData = sessionManager.loadSession(currentSessionId);
      let messageSequence = sessionData ? sessionData.metadata.messageCount + 1 : 1;

      // Create enhanced payload matching WebhookPayload interface
      const payload: WebhookPayload = {
        message: mensagem,
        session_id: currentSessionId, // Always present
        user_id: finalUserId,
        context: {
          timestamp: new Date().toISOString(),
          fonte: 'plasmo_extension_webhook',
          user_agent: navigator.userAgent,
          message_sequence: messageSequence,
          session_metadata: sessionData?.metadata || {
            userId: finalUserId,
            createdAt: new Date().toISOString(),
            lastActivity: new Date().toISOString(),
            messageCount: 0,
            status: 'active'
          },
          ...contextoAdicional,
        },
      };

      let rawResponse = await fazerRequisicaoWebhook<any>(
        webhookUrl,
        {
          method: 'POST',
          body: JSON.stringify(payload),
        }
      );

      // Debug: Log the raw response to understand N8N format
      console.log('[WebhookAPI] Raw N8N response:', rawResponse);
      console.log('[WebhookAPI] Response type:', typeof rawResponse);
      console.log('[WebhookAPI] Is array:', Array.isArray(rawResponse));
      if (rawResponse && typeof rawResponse === 'object') {
        console.log('[WebhookAPI] Response keys:', Object.keys(rawResponse));
      }

      // Simplified and more robust N8N response parsing
      let response: WebhookResponse;
      let responseText: string = '';

      try {
        // Based on your N8N screenshot, the response should be a direct object with "output" field
        if (rawResponse && typeof rawResponse === 'object') {
          // Check for direct output field first (most common N8N format)
          if (rawResponse.output && typeof rawResponse.output === 'string') {
            responseText = rawResponse.output;
          }
          // Check if it's an array with output in first element
          else if (Array.isArray(rawResponse) && rawResponse.length > 0 && rawResponse[0]?.output) {
            responseText = rawResponse[0].output;
          }
          // Fallback to other common response fields
          else if (rawResponse.response) {
            responseText = rawResponse.response;
          }
          else if (rawResponse.message) {
            responseText = rawResponse.message;
          }
          else if (rawResponse.text) {
            responseText = rawResponse.text;
          }
          // If it's a direct string response
          else if (typeof rawResponse === 'string') {
            responseText = rawResponse;
          }
        }
        // Handle direct string response
        else if (typeof rawResponse === 'string') {
          responseText = rawResponse;
        }

        // Clean up the response text
        if (responseText) {
          // Check for unprocessed N8N template syntax and provide helpful error
          if (responseText.includes('{{ $json.output }}') || responseText.includes('{{ $json.') || responseText.includes('{{ $now')) {
            console.error('[WebhookAPI] Detected unprocessed N8N template syntax in response:', responseText);

            // Provide a more helpful error message with N8N configuration guidance
            const errorMessage = `N8N Configuration Error: The workflow is returning unprocessed template syntax "${responseText}".

Fix this in N8N:
1. In the "Respond to Webhook" node, make sure you're using Expression mode
2. Set the output field to: ={{ $json.output }} (with equals sign)
3. Or return JSON format: {"output": "={{ $json.output }}"}
4. Verify the SOGE Agent node is outputting to the 'output' field

Current raw response: ${JSON.stringify(rawResponse)}`;

            throw new WebhookError(errorMessage, 422, { rawResponse, detectedTemplate: responseText });
          }

          // Clean up escaped characters
          responseText = responseText
            .replace(/\\n/g, '\n')
            .replace(/\\"/g, '"')
            .trim();
        }

        // If we still don't have response text, log detailed info and throw error
        if (!responseText) {
          console.error('[WebhookAPI] Could not extract response text from N8N response:', {
            rawResponse,
            responseType: typeof rawResponse,
            isArray: Array.isArray(rawResponse),
            keys: rawResponse && typeof rawResponse === 'object' ? Object.keys(rawResponse) : 'N/A',
            stringified: JSON.stringify(rawResponse).substring(0, 500)
          });

          throw new WebhookError(
            `N8N webhook response format not recognized. Expected response with 'output' field. Received: ${typeof rawResponse}. Check N8N workflow output format.`,
            422,
            rawResponse
          );
        }

      } catch (parseError) {
        console.error('[WebhookAPI] Error parsing N8N response:', parseError);
        if (parseError instanceof WebhookError) {
          throw parseError;
        }
        throw new WebhookError(
          `Failed to parse N8N webhook response: ${parseError instanceof Error ? parseError.message : 'Unknown parsing error'}`,
          422,
          { rawResponse, parseError }
        );
      }

      response = {
        response: responseText,
        session_id: currentSessionId,
        message_id: `msg_${Date.now()}`,
        timestamp: new Date().toISOString(),
        status: 'success'
      };

      // For N8N webhooks, skip complex session validation since N8N only returns output
      // Session management is handled locally by SessionManager

      // Validate that we have a valid response after potential recovery
      if (!response.response) {
        throw new WebhookError('Invalid response from N8N: missing response content', 422, response);
      }

      // Process session_id from N8N response (Requirement 2.4)
      const finalSessionId = response.session_id || currentSessionId;

      // If N8N returned a different session_id, update our records
      if (response.session_id && response.session_id !== currentSessionId) {
        console.log(`[WebhookAPI] N8N returned different session_id: ${response.session_id}, updating local records`);
        currentSessionId = response.session_id;

        // Ensure the new session exists in our storage
        const existingSession = sessionManager.loadSession(currentSessionId);
        if (!existingSession) {
          // Create session record for N8N-provided session_id
          sessionManager.createNewSession(finalUserId);
          // Override the generated ID with N8N's ID (this is a special case)
          sessionData = {
            sessionId: currentSessionId,
            metadata: {
              userId: finalUserId,
              createdAt: new Date().toISOString(),
              lastActivity: new Date().toISOString(),
              messageCount: 0,
              status: 'active'
            },
            messages: []
          };
          sessionManager.persistSession(currentSessionId, sessionData.metadata);
        }
      }

      // Update session activity after successful message processing
      sessionManager.updateSessionActivity(currentSessionId);

      // Create user message for session storage
      const userMessage: StoredMessage = {
        id: `user_${Date.now()}`,
        sessionId: currentSessionId,
        type: 'user',
        content: mensagem,
        timestamp: new Date().toISOString(),
        sequence: messageSequence
      };

      // Create assistant message for session storage
      const assistantMessage: StoredMessage = {
        id: response.message_id || `assistant_${Date.now()}`,
        sessionId: finalSessionId,
        type: 'assistant',
        content: response.response,
        timestamp: response.timestamp || new Date().toISOString(),
        sequence: messageSequence + 1
      };

      // Add messages to session
      sessionManager.addMessageToSession(currentSessionId, userMessage);
      sessionManager.addMessageToSession(currentSessionId, assistantMessage);

      // Log successful session processing if detailed logging is enabled
      const storageConfig = sessionManager.getStorageConfig();
      if (storageConfig.enableDetailedLogging) {
        console.log(`[WebhookAPI] Message processed successfully:`, {
          sessionId: finalSessionId,
          messageSequence,
          processingTime: response.processing_time,
          responseStatus: response.status || 'success'
        });
      }

      // Adapt response to expected format
      const respostaAdaptada: RespostaEnviarMensagem = {
        sucesso: true,
        sessao_id: finalSessionId,
        mensagem_usuario: {
          id: userMessage.id,
          sessao_id: currentSessionId,
          tipo: 'user',
          conteudo: mensagem,
          timestamp: userMessage.timestamp
        },
        mensagem_assistente: {
          id: assistantMessage.id,
          sessao_id: finalSessionId,
          tipo: 'assistant',
          conteudo: response.response,
          timestamp: assistantMessage.timestamp
        },
        tempo_processamento: response.processing_time || 0
      };

      return respostaAdaptada;
    } catch (error) {
      // Enhanced error handling with session context
      let mensagemErro: string;

      if (error instanceof WebhookError) {
        mensagemErro = error.message;

        // Log detailed error information if enabled
        const storageConfig = sessionManager.getStorageConfig();
        if (storageConfig.enableDetailedLogging) {
          console.error(`[WebhookAPI] Webhook error:`, {
            message: error.message,
            status: error.status,
            details: error.details,
            sessionId: sessaoId || sessionManager.getCurrentSessionId()
          });
        }
      } else {
        mensagemErro = 'Erro de conexão com o webhook N8N';
        console.error(`[WebhookAPI] Unexpected error:`, error);
      }

      setErro(mensagemErro);
      throw error;
    } finally {
      setCarregando(false);
    }
  }, [webhookUrl, fazerRequisicaoWebhook]);

  const testarWebhook = useCallback(async (): Promise<boolean> => {
    setCarregando(true);
    setErro(null);

    try {
      const testUserId = "test_user";

      // Create a temporary session for testing
      const testSessionId = sessionManager.createNewSession(testUserId);

      // Create test payload matching WebhookPayload interface
      const payload: WebhookPayload = {
        message: "Teste de conexão",
        session_id: testSessionId,
        user_id: testUserId,
        context: {
          timestamp: new Date().toISOString(),
          fonte: 'test_connection',
          user_agent: navigator.userAgent,
          message_sequence: 1,
          session_metadata: {
            userId: testUserId,
            createdAt: new Date().toISOString(),
            lastActivity: new Date().toISOString(),
            messageCount: 0,
            status: 'active'
          }
        }
      };

      const rawResponse = await fazerRequisicaoWebhook<any>(
        webhookUrl,
        {
          method: 'POST',
          body: JSON.stringify(payload),
        }
      );

      // Handle N8N response format for test
      let response: WebhookResponse;
      if (Array.isArray(rawResponse) && rawResponse.length > 0 && rawResponse[0].output) {
        // N8N format: extract output from array
        response = {
          response: rawResponse[0].output,
          session_id: testSessionId,
          message_id: `test_msg_${Date.now()}`,
          timestamp: new Date().toISOString(),
          status: 'success'
        };
      } else if (rawResponse.response) {
        // Standard format
        response = rawResponse as WebhookResponse;
      } else {
        const errorMessage = 'Test failed - Invalid response format from N8N';
        setErro(errorMessage);
        sessionManager.finalizeSession(testSessionId);
        return false;
      }

      // For N8N webhooks, skip session validation in tests too

      // Validate response content
      if (!response.response) {
        const errorMessage = 'Test failed - Invalid response from N8N: missing response content';
        setErro(errorMessage);
        sessionManager.finalizeSession(testSessionId);
        return false;
      }

      // Log successful test if detailed logging is enabled
      const storageConfig = sessionManager.getStorageConfig();
      if (storageConfig.enableDetailedLogging) {
        console.log(`[WebhookAPI] Test successful:`, {
          sessionId: response.session_id || testSessionId,
          processingTime: response.processing_time,
          responseStatus: response.status || 'success'
        });
      }

      // Clean up test session after successful test
      sessionManager.finalizeSession(testSessionId);

      return true;
    } catch (error) {
      // Enhanced error handling for test
      let mensagemErro: string;

      if (error instanceof WebhookError) {
        mensagemErro = `Test failed - ${error.message}`;

        // Log detailed test error information if enabled
        const storageConfig = sessionManager.getStorageConfig();
        if (storageConfig.enableDetailedLogging) {
          console.error(`[WebhookAPI] Test webhook error:`, {
            message: error.message,
            status: error.status,
            details: error.details
          });
        }
      } else {
        mensagemErro = 'Test failed - Erro ao testar webhook';
        console.error(`[WebhookAPI] Unexpected test error:`, error);
      }

      setErro(mensagemErro);
      return false;
    } finally {
      setCarregando(false);
    }
  }, [webhookUrl, fazerRequisicaoWebhook]);

  const limparErro = useCallback(() => {
    setErro(null);
  }, []);

  // Simple connectivity test that doesn't require a full webhook setup
  const testarConectividade = useCallback(async (): Promise<{ conectado: boolean; detalhes: string }> => {
    try {
      // First, try a simple HEAD request to check if the server is reachable
      const controller = new AbortController();
      const timeoutId = setTimeout(() => controller.abort(), 10000);

      const response = await fetch(webhookUrl, {
        method: 'HEAD',
        signal: controller.signal,
      });

      clearTimeout(timeoutId);

      if (response.ok) {
        return {
          conectado: true,
          detalhes: `Servidor N8N acessível (${response.status})`
        };
      } else if (response.status === 404) {
        return {
          conectado: false,
          detalhes: `Webhook "chat" não encontrado. Verifique se está configurado no N8N.`
        };
      } else {
        return {
          conectado: false,
          detalhes: `Servidor respondeu com status ${response.status}`
        };
      }
    } catch (error) {
      if (error instanceof Error) {
        if (error.name === 'AbortError') {
          return {
            conectado: false,
            detalhes: 'Timeout - servidor N8N não responde'
          };
        } else if (error.message.includes('fetch')) {
          return {
            conectado: false,
            detalhes: 'Não foi possível conectar ao servidor N8N'
          };
        }
      }
      return {
        conectado: false,
        detalhes: `Erro de conectividade: ${error instanceof Error ? error.message : 'Erro desconhecido'}`
      };
    }
  }, [webhookUrl]);

  return {
    // Estados
    carregando,
    erro,

    // Métodos da API
    enviarMensagem: enviarMensagemWebhook,
    testarWebhook,
    testarConectividade,

    // Utilitários
    limparErro,
  };
};
