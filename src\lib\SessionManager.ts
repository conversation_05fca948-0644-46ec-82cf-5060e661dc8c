/**
 * SessionManager - Utility class for managing webhook session lifecycle
 * Handles session creation, persistence, and retrieval for N8N webhook communication
 */

import { sessionStorage, type SessionStorageConfig, type StorageStats } from './SessionStorage';

// Session data structure interfaces
export interface SessionMetadata {
  userId: string;
  createdAt: string;
  lastActivity: string;
  messageCount: number;
  status: 'active' | 'finalized';
}

export interface StoredMessage {
  id: string;
  sessionId: string;
  type: 'user' | 'assistant';
  content: string;
  timestamp: string;
  sequence: number;
}

export interface StoredSession {
  sessionId: string;
  userId: string;
  createdAt: string;
  lastActivity: string;
  messageCount: number;
  status: 'active' | 'finalized';
  messages: StoredMessage[];
}

export interface SessionData {
  sessionId: string;
  metadata: SessionMetadata;
  messages: StoredMessage[];
}

export interface SessionError {
  type: 'connection' | 'invalid_session' | 'storage' | 'n8n_error';
  sessionId: string;
  message: string;
  timestamp: string;
  recoveryAction: 'retry' | 'new_session' | 'fallback';
}

// Storage keys for localStorage (maintained for backward compatibility)
const STORAGE_KEYS = {
  ACTIVE_SESSION: 'webhook_active_session',
  SESSION_HISTORY: 'webhook_session_history',
  SESSION_METADATA: 'webhook_session_metadata'
} as const;

/**
 * SessionManager class for handling webhook session lifecycle
 * Enhanced with robust storage utilities and configuration-based cleanup
 */
export class SessionManager {
  private currentSessionId: string | null = null;
  private storageConfig: SessionStorageConfig;

  constructor(config?: Partial<SessionStorageConfig>) {
    // Load existing configuration or use defaults
    this.storageConfig = sessionStorage.loadStorageConfig();
    
    // Apply any provided configuration overrides
    if (config) {
      this.updateStorageConfig(config);
    }

    // Perform automatic cleanup on initialization if configured
    this.performScheduledCleanup();
  }

  /**
   * Update storage configuration
   */
  updateStorageConfig(config: Partial<SessionStorageConfig>): void {
    sessionStorage.updateConfig(config);
    this.storageConfig = sessionStorage.getConfig();
  }

  /**
   * Get current storage configuration
   */
  getStorageConfig(): SessionStorageConfig {
    return { ...this.storageConfig };
  }

  /**
   * Get storage statistics
   */
  getStorageStats(): StorageStats | null {
    const result = sessionStorage.getStorageStats();
    return result.success ? result.data || null : null;
  }

  /**
   * Get the current active session ID
   */
  getCurrentSessionId(): string | null {
    if (this.currentSessionId) {
      return this.currentSessionId;
    }

    // Try to load from localStorage
    try {
      const activeSession = localStorage.getItem(STORAGE_KEYS.ACTIVE_SESSION);
      if (activeSession) {
        const sessionData = JSON.parse(activeSession);
        this.currentSessionId = sessionData.sessionId;
        return this.currentSessionId;
      }
    } catch (error) {
      console.warn('Failed to load active session from storage:', error);
    }

    return null;
  }

  /**
   * Create a new session with unique timestamp-based ID
   */
  createNewSession(userId: string): string {
    const sessionId = this.generateSessionId(userId);
    const now = new Date().toISOString();
    
    const session: StoredSession = {
      sessionId,
      userId,
      createdAt: now,
      lastActivity: now,
      messageCount: 0,
      status: 'active',
      messages: []
    };

    // Set as current session
    this.currentSessionId = sessionId;
    
    // Persist the new session using enhanced storage
    const storeResult = sessionStorage.storeSession(session);
    if (!storeResult.success) {
      console.error('Failed to store new session:', storeResult.error);
      // Fallback to basic storage
      this.fallbackPersistSession(sessionId, {
        userId,
        createdAt: now,
        lastActivity: now,
        messageCount: 0,
        status: 'active'
      });
    }
    
    // Store as active session
    try {
      localStorage.setItem(STORAGE_KEYS.ACTIVE_SESSION, JSON.stringify({
        sessionId,
        createdAt: now
      }));
    } catch (error) {
      console.error('Failed to store active session:', error);
    }

    return sessionId;
  }

  /**
   * Persist session metadata to localStorage (enhanced version)
   */
  persistSession(sessionId: string, metadata: SessionMetadata): void {
    // Try to get existing session first
    const existingResult = sessionStorage.getSession(sessionId);
    
    const session: StoredSession = {
      sessionId,
      userId: metadata.userId,
      createdAt: metadata.createdAt,
      lastActivity: metadata.lastActivity,
      messageCount: metadata.messageCount,
      status: metadata.status,
      messages: existingResult.success && existingResult.data ? existingResult.data.messages : []
    };

    const storeResult = sessionStorage.storeSession(session);
    if (!storeResult.success) {
      console.error('Enhanced storage failed, using fallback:', storeResult.error);
      this.fallbackPersistSession(sessionId, metadata);
    }
  }

  /**
   * Fallback persistence method (original implementation)
   */
  private fallbackPersistSession(sessionId: string, metadata: SessionMetadata): void {
    try {
      // Get existing session history
      const historyJson = localStorage.getItem(STORAGE_KEYS.SESSION_HISTORY);
      const history: Record<string, StoredSession> = historyJson ? JSON.parse(historyJson) : {};

      // Update or create session entry
      const existingSession = history[sessionId];
      const updatedSession: StoredSession = {
        sessionId,
        userId: metadata.userId,
        createdAt: metadata.createdAt,
        lastActivity: metadata.lastActivity,
        messageCount: metadata.messageCount,
        status: metadata.status,
        messages: existingSession?.messages || []
      };

      history[sessionId] = updatedSession;

      // Store updated history
      localStorage.setItem(STORAGE_KEYS.SESSION_HISTORY, JSON.stringify(history));

      // Store metadata separately for quick access
      const metadataJson = localStorage.getItem(STORAGE_KEYS.SESSION_METADATA);
      const metadataStore: Record<string, SessionMetadata> = metadataJson ? JSON.parse(metadataJson) : {};
      metadataStore[sessionId] = metadata;
      localStorage.setItem(STORAGE_KEYS.SESSION_METADATA, JSON.stringify(metadataStore));

    } catch (error) {
      console.error('Failed to persist session:', error);
      throw new Error(`Session persistence failed: ${error}`);
    }
  }

  /**
   * Load session data by session ID (enhanced version)
   */
  loadSession(sessionId: string): SessionData | null {
    // Try enhanced storage first
    const result = sessionStorage.getSession(sessionId);
    if (result.success && result.data) {
      const session = result.data;
      return {
        sessionId: session.sessionId,
        metadata: {
          userId: session.userId,
          createdAt: session.createdAt,
          lastActivity: session.lastActivity,
          messageCount: session.messageCount,
          status: session.status
        },
        messages: session.messages
      };
    }

    // Fallback to original method
    return this.fallbackLoadSession(sessionId);
  }

  /**
   * Fallback load method (original implementation)
   */
  private fallbackLoadSession(sessionId: string): SessionData | null {
    try {
      const historyJson = localStorage.getItem(STORAGE_KEYS.SESSION_HISTORY);
      if (!historyJson) {
        return null;
      }

      const history: Record<string, StoredSession> = JSON.parse(historyJson);
      const storedSession = history[sessionId];
      
      if (!storedSession) {
        return null;
      }

      const metadata: SessionMetadata = {
        userId: storedSession.userId,
        createdAt: storedSession.createdAt,
        lastActivity: storedSession.lastActivity,
        messageCount: storedSession.messageCount,
        status: storedSession.status
      };

      return {
        sessionId: storedSession.sessionId,
        metadata,
        messages: storedSession.messages
      };

    } catch (error) {
      console.error('Failed to load session:', error);
      return null;
    }
  }

  /**
   * Finalize a session (mark as completed)
   */
  finalizeSession(sessionId: string): void {
    try {
      const sessionData = this.loadSession(sessionId);
      if (!sessionData) {
        console.warn(`Session ${sessionId} not found for finalization`);
        return;
      }

      // Update metadata to finalized status
      const updatedMetadata: SessionMetadata = {
        ...sessionData.metadata,
        status: 'finalized',
        lastActivity: new Date().toISOString()
      };

      this.persistSession(sessionId, updatedMetadata);

      // Clear active session if this was the active one
      if (this.currentSessionId === sessionId) {
        this.currentSessionId = null;
        localStorage.removeItem(STORAGE_KEYS.ACTIVE_SESSION);
      }

    } catch (error) {
      console.error('Failed to finalize session:', error);
    }
  }

  /**
   * Generate unique session ID using timestamp-based algorithm
   */
  private generateSessionId(userId: string): string {
    const timestamp = Date.now();
    const random = Math.random().toString(36).substring(2, 8);
    return `session_${userId}_${timestamp}_${random}`;
  }

  /**
   * Update session activity (increment message count, update timestamp)
   */
  updateSessionActivity(sessionId: string): void {
    const sessionData = this.loadSession(sessionId);
    if (!sessionData) {
      console.warn(`Session ${sessionId} not found for activity update`);
      return;
    }

    const updatedMetadata: SessionMetadata = {
      ...sessionData.metadata,
      messageCount: sessionData.metadata.messageCount + 1,
      lastActivity: new Date().toISOString()
    };

    this.persistSession(sessionId, updatedMetadata);
  }

  /**
   * Add message to session (enhanced version)
   */
  addMessageToSession(sessionId: string, message: StoredMessage): void {
    // Try enhanced storage first
    const result = sessionStorage.addMessageToSession(sessionId, message);
    if (result.success) {
      return;
    }

    console.warn('Enhanced message storage failed, using fallback:', result.error);
    this.fallbackAddMessage(sessionId, message);
  }

  /**
   * Fallback method for adding messages (original implementation)
   */
  private fallbackAddMessage(sessionId: string, message: StoredMessage): void {
    try {
      const historyJson = localStorage.getItem(STORAGE_KEYS.SESSION_HISTORY);
      const history: Record<string, StoredSession> = historyJson ? JSON.parse(historyJson) : {};
      
      const session = history[sessionId];
      if (!session) {
        console.warn(`Session ${sessionId} not found for message addition`);
        return;
      }

      session.messages.push(message);
      session.messageCount = session.messages.length;
      session.lastActivity = new Date().toISOString();

      history[sessionId] = session;
      localStorage.setItem(STORAGE_KEYS.SESSION_HISTORY, JSON.stringify(history));

      // Update metadata as well
      const updatedMetadata: SessionMetadata = {
        userId: session.userId,
        createdAt: session.createdAt,
        lastActivity: session.lastActivity,
        messageCount: session.messageCount,
        status: session.status
      };

      this.persistSession(sessionId, updatedMetadata);

    } catch (error) {
      console.error('Failed to add message to session:', error);
    }
  }

  /**
   * Get all session metadata for history display (enhanced version)
   */
  getAllSessionsMetadata(): SessionMetadata[] {
    // Try enhanced storage first
    const result = sessionStorage.getAllSessionMetadata();
    if (result.success && result.data) {
      return result.data;
    }

    console.warn('Enhanced metadata retrieval failed, using fallback:', result.error);
    return this.fallbackGetAllMetadata();
  }

  /**
   * Fallback method for getting all metadata (original implementation)
   */
  private fallbackGetAllMetadata(): SessionMetadata[] {
    try {
      const metadataJson = localStorage.getItem(STORAGE_KEYS.SESSION_METADATA);
      if (!metadataJson) {
        return [];
      }

      const metadataStore: Record<string, SessionMetadata> = JSON.parse(metadataJson);
      return Object.values(metadataStore).sort((a, b) => 
        new Date(b.lastActivity).getTime() - new Date(a.lastActivity).getTime()
      );

    } catch (error) {
      console.error('Failed to get sessions metadata:', error);
      return [];
    }
  }

  /**
   * Clean up old sessions based on configuration
   */
  cleanupOldSessions(maxAgeInDays?: number): { removedSessions: number; freedSpace: number } {
    const ageLimit = maxAgeInDays || this.storageConfig.autoCleanupDays;
    
    // Try enhanced cleanup first
    const result = sessionStorage.performAutomaticCleanup();
    if (result.success && result.data) {
      return result.data;
    }

    console.warn('Enhanced cleanup failed, using fallback:', result.error);
    return this.fallbackCleanup(ageLimit);
  }

  /**
   * Fallback cleanup method (original implementation)
   */
  private fallbackCleanup(maxAgeInDays: number): { removedSessions: number; freedSpace: number } {
    try {
      const cutoffDate = new Date();
      cutoffDate.setDate(cutoffDate.getDate() - maxAgeInDays);
      const cutoffTimestamp = cutoffDate.toISOString();

      const historyJson = localStorage.getItem(STORAGE_KEYS.SESSION_HISTORY);
      const metadataJson = localStorage.getItem(STORAGE_KEYS.SESSION_METADATA);

      if (!historyJson || !metadataJson) {
        return { removedSessions: 0, freedSpace: 0 };
      }

      const history: Record<string, StoredSession> = JSON.parse(historyJson);
      const metadata: Record<string, SessionMetadata> = JSON.parse(metadataJson);

      const initialCount = Object.keys(history).length;
      const sessionsToKeep: Record<string, StoredSession> = {};
      const metadataToKeep: Record<string, SessionMetadata> = {};

      Object.entries(history).forEach(([sessionId, session]) => {
        if (session.lastActivity > cutoffTimestamp || session.status === 'active') {
          sessionsToKeep[sessionId] = session;
          if (metadata[sessionId]) {
            metadataToKeep[sessionId] = metadata[sessionId];
          }
        }
      });

      localStorage.setItem(STORAGE_KEYS.SESSION_HISTORY, JSON.stringify(sessionsToKeep));
      localStorage.setItem(STORAGE_KEYS.SESSION_METADATA, JSON.stringify(metadataToKeep));

      const finalCount = Object.keys(sessionsToKeep).length;
      const removedSessions = initialCount - finalCount;

      return { removedSessions, freedSpace: 0 }; // Simplified for fallback

    } catch (error) {
      console.error('Failed to cleanup old sessions:', error);
      return { removedSessions: 0, freedSpace: 0 };
    }
  }

  /**
   * Perform scheduled cleanup based on configuration
   */
  private performScheduledCleanup(): void {
    try {
      const stats = this.getStorageStats();
      if (!stats) {
        return;
      }

      // Check if cleanup is needed based on configuration
      const shouldCleanup = stats.totalSessions > this.storageConfig.maxStoredSessions ||
                           this.shouldPerformTimeBasedCleanup(stats.lastCleanup);

      if (shouldCleanup) {
        const result = this.cleanupOldSessions();
        if (this.storageConfig.enableDetailedLogging) {
          console.log('[SessionManager] Scheduled cleanup completed:', result);
        }
      }

    } catch (error) {
      console.error('Failed to perform scheduled cleanup:', error);
    }
  }

  /**
   * Check if time-based cleanup should be performed
   */
  private shouldPerformTimeBasedCleanup(lastCleanup: string): boolean {
    if (lastCleanup === 'Never') {
      return true;
    }

    try {
      const lastCleanupDate = new Date(lastCleanup);
      const daysSinceCleanup = (Date.now() - lastCleanupDate.getTime()) / (1000 * 60 * 60 * 24);
      return daysSinceCleanup >= this.storageConfig.autoCleanupDays;
    } catch (error) {
      return true; // Cleanup if we can't parse the date
    }
  }

  /**
   * Force immediate cleanup regardless of configuration
   */
  forceCleanup(): { removedSessions: number; freedSpace: number } {
    return this.cleanupOldSessions(this.storageConfig.autoCleanupDays);
  }

  /**
   * Clear all session data (use with caution)
   */
  clearAllSessions(): boolean {
    const result = sessionStorage.clearAllSessions();
    if (result.success) {
      this.currentSessionId = null;
      return true;
    }
    
    console.error('Failed to clear all sessions:', result.error);
    return false;
  }
}

// Export singleton instance
export const sessionManager = new SessionManager();