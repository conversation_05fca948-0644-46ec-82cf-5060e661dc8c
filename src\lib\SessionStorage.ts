/**
 * SessionStorage - Enhanced storage utilities for webhook session management
 * Provides robust localStorage operations with error handling and data validation
 */

import type { StoredSession, SessionMetadata, StoredMessage } from './SessionManager';

// Storage configuration interface
export interface SessionStorageConfig {
  maxStoredSessions: number;
  autoCleanupDays: number;
  enableDetailedLogging: boolean;
  compressionEnabled: boolean;
}

// Default storage configuration
export const DEFAULT_STORAGE_CONFIG: SessionStorageConfig = {
  maxStoredSessions: 100,
  autoCleanupDays: 30,
  enableDetailedLogging: false,
  compressionEnabled: false
};

// Storage keys for localStorage
export const STORAGE_KEYS = {
  ACTIVE_SESSION: 'webhook_active_session',
  SESSION_HISTORY: 'webhook_session_history',
  SESSION_METADATA: 'webhook_session_metadata',
  STORAGE_CONFIG: 'webhook_storage_config',
  STORAGE_STATS: 'webhook_storage_stats'
} as const;

// Storage statistics interface
export interface StorageStats {
  totalSessions: number;
  activeSessions: number;
  finalizedSessions: number;
  totalMessages: number;
  storageSize: number;
  lastCleanup: string;
  lastAccess: string;
}

// Storage operation result
export interface StorageResult<T = any> {
  success: boolean;
  data?: T;
  error?: string;
  timestamp: string;
}

/**
 * Enhanced session storage utility class
 */
export class SessionStorage {
  private config: SessionStorageConfig;
  private logger: (message: string, data?: any) => void;

  constructor(config: Partial<SessionStorageConfig> = {}) {
    this.config = { ...DEFAULT_STORAGE_CONFIG, ...config };
    this.logger = this.config.enableDetailedLogging 
      ? (message: string, data?: any) => console.log(`[SessionStorage] ${message}`, data)
      : () => {};
  }

  /**
   * Update storage configuration
   */
  updateConfig(newConfig: Partial<SessionStorageConfig>): void {
    this.config = { ...this.config, ...newConfig };
    this.saveStorageConfig();
    this.logger('Configuration updated', this.config);
  }

  /**
   * Get current storage configuration
   */
  getConfig(): SessionStorageConfig {
    return { ...this.config };
  }

  /**
   * Save storage configuration to localStorage
   */
  private saveStorageConfig(): void {
    try {
      localStorage.setItem(STORAGE_KEYS.STORAGE_CONFIG, JSON.stringify(this.config));
    } catch (error) {
      this.logger('Failed to save storage config', error);
    }
  }

  /**
   * Load storage configuration from localStorage
   */
  loadStorageConfig(): SessionStorageConfig {
    try {
      const configJson = localStorage.getItem(STORAGE_KEYS.STORAGE_CONFIG);
      if (configJson) {
        const loadedConfig = JSON.parse(configJson);
        this.config = { ...DEFAULT_STORAGE_CONFIG, ...loadedConfig };
        this.logger('Configuration loaded', this.config);
      }
    } catch (error) {
      this.logger('Failed to load storage config, using defaults', error);
      this.config = DEFAULT_STORAGE_CONFIG;
    }
    return this.config;
  }

  /**
   * Store session data with enhanced error handling
   */
  storeSession(session: StoredSession): StorageResult<void> {
    const timestamp = new Date().toISOString();
    
    try {
      // Get existing sessions
      const historyResult = this.getSessionHistory();
      if (!historyResult.success) {
        return {
          success: false,
          error: `Failed to load session history: ${historyResult.error}`,
          timestamp
        };
      }

      const history = historyResult.data || {};
      
      // Check storage limits before adding
      if (!history[session.sessionId] && Object.keys(history).length >= this.config.maxStoredSessions) {
        this.logger('Storage limit reached, cleaning up old sessions');
        this.performAutomaticCleanup();
      }

      // Store the session
      history[session.sessionId] = session;
      localStorage.setItem(STORAGE_KEYS.SESSION_HISTORY, JSON.stringify(history));

      // Update metadata store
      const metadata: SessionMetadata = {
        userId: session.userId,
        createdAt: session.createdAt,
        lastActivity: session.lastActivity,
        messageCount: session.messageCount,
        status: session.status
      };

      const metadataResult = this.storeSessionMetadata(session.sessionId, metadata);
      if (!metadataResult.success) {
        this.logger('Warning: Failed to store session metadata', metadataResult.error);
      }

      // Update storage statistics
      this.updateStorageStats();

      this.logger('Session stored successfully', { sessionId: session.sessionId });
      
      return {
        success: true,
        timestamp
      };

    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown storage error';
      this.logger('Failed to store session', { sessionId: session.sessionId, error: errorMessage });
      
      return {
        success: false,
        error: errorMessage,
        timestamp
      };
    }
  }

  /**
   * Retrieve session data with validation
   */
  getSession(sessionId: string): StorageResult<StoredSession> {
    const timestamp = new Date().toISOString();
    
    try {
      const historyResult = this.getSessionHistory();
      if (!historyResult.success) {
        return {
          success: false,
          error: `Failed to load session history: ${historyResult.error}`,
          timestamp
        };
      }

      const history = historyResult.data || {};
      const session = history[sessionId];

      if (!session) {
        return {
          success: false,
          error: 'Session not found',
          timestamp
        };
      }

      // Validate session data structure
      if (!this.validateSessionData(session)) {
        return {
          success: false,
          error: 'Invalid session data structure',
          timestamp
        };
      }

      this.logger('Session retrieved successfully', { sessionId });
      
      return {
        success: true,
        data: session,
        timestamp
      };

    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown retrieval error';
      this.logger('Failed to retrieve session', { sessionId, error: errorMessage });
      
      return {
        success: false,
        error: errorMessage,
        timestamp
      };
    }
  }

  /**
   * Get all session history
   */
  getSessionHistory(): StorageResult<Record<string, StoredSession>> {
    const timestamp = new Date().toISOString();
    
    try {
      const historyJson = localStorage.getItem(STORAGE_KEYS.SESSION_HISTORY);
      const history: Record<string, StoredSession> = historyJson ? JSON.parse(historyJson) : {};
      
      return {
        success: true,
        data: history,
        timestamp
      };

    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown error loading history';
      this.logger('Failed to load session history', error);
      
      return {
        success: false,
        error: errorMessage,
        timestamp
      };
    }
  }

  /**
   * Store session metadata separately for quick access
   */
  storeSessionMetadata(sessionId: string, metadata: SessionMetadata): StorageResult<void> {
    const timestamp = new Date().toISOString();
    
    try {
      const metadataJson = localStorage.getItem(STORAGE_KEYS.SESSION_METADATA);
      const metadataStore: Record<string, SessionMetadata> = metadataJson ? JSON.parse(metadataJson) : {};
      
      metadataStore[sessionId] = metadata;
      localStorage.setItem(STORAGE_KEYS.SESSION_METADATA, JSON.stringify(metadataStore));
      
      this.logger('Session metadata stored', { sessionId });
      
      return {
        success: true,
        timestamp
      };

    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown metadata storage error';
      this.logger('Failed to store session metadata', { sessionId, error: errorMessage });
      
      return {
        success: false,
        error: errorMessage,
        timestamp
      };
    }
  }

  /**
   * Get all session metadata
   */
  getAllSessionMetadata(): StorageResult<SessionMetadata[]> {
    const timestamp = new Date().toISOString();
    
    try {
      const metadataJson = localStorage.getItem(STORAGE_KEYS.SESSION_METADATA);
      if (!metadataJson) {
        return {
          success: true,
          data: [],
          timestamp
        };
      }

      const metadataStore: Record<string, SessionMetadata> = JSON.parse(metadataJson);
      const metadataList = Object.values(metadataStore).sort((a, b) => 
        new Date(b.lastActivity).getTime() - new Date(a.lastActivity).getTime()
      );

      return {
        success: true,
        data: metadataList,
        timestamp
      };

    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown error loading metadata';
      this.logger('Failed to load session metadata', error);
      
      return {
        success: false,
        error: errorMessage,
        timestamp
      };
    }
  }

  /**
   * Add message to session with validation
   */
  addMessageToSession(sessionId: string, message: StoredMessage): StorageResult<void> {
    const timestamp = new Date().toISOString();
    
    try {
      const sessionResult = this.getSession(sessionId);
      if (!sessionResult.success || !sessionResult.data) {
        return {
          success: false,
          error: `Session not found: ${sessionResult.error}`,
          timestamp
        };
      }

      const session = sessionResult.data;
      
      // Validate message structure
      if (!this.validateMessageData(message)) {
        return {
          success: false,
          error: 'Invalid message data structure',
          timestamp
        };
      }

      // Add message and update session
      session.messages.push(message);
      session.messageCount = session.messages.length;
      session.lastActivity = timestamp;

      // Store updated session
      const storeResult = this.storeSession(session);
      if (!storeResult.success) {
        return storeResult;
      }

      this.logger('Message added to session', { sessionId, messageId: message.id });
      
      return {
        success: true,
        timestamp
      };

    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown error adding message';
      this.logger('Failed to add message to session', { sessionId, error: errorMessage });
      
      return {
        success: false,
        error: errorMessage,
        timestamp
      };
    }
  }

  /**
   * Perform automatic cleanup based on configuration
   */
  performAutomaticCleanup(): StorageResult<{ removedSessions: number; freedSpace: number }> {
    const timestamp = new Date().toISOString();
    
    try {
      const cutoffDate = new Date();
      cutoffDate.setDate(cutoffDate.getDate() - this.config.autoCleanupDays);
      const cutoffTimestamp = cutoffDate.toISOString();

      const historyResult = this.getSessionHistory();
      if (!historyResult.success) {
        return {
          success: false,
          error: `Failed to load session history: ${historyResult.error}`,
          timestamp
        };
      }

      const history = historyResult.data || {};
      const metadataJson = localStorage.getItem(STORAGE_KEYS.SESSION_METADATA);
      const metadata: Record<string, SessionMetadata> = metadataJson ? JSON.parse(metadataJson) : {};

      const sessionsToKeep: Record<string, StoredSession> = {};
      const metadataToKeep: Record<string, SessionMetadata> = {};
      let removedCount = 0;
      const initialSize = this.calculateStorageSize();

      // Keep sessions that are recent or still active
      Object.entries(history).forEach(([sessionId, session]) => {
        const shouldKeep = session.lastActivity > cutoffTimestamp || 
                          session.status === 'active' ||
                          Object.keys(sessionsToKeep).length < this.config.maxStoredSessions;

        if (shouldKeep) {
          sessionsToKeep[sessionId] = session;
          if (metadata[sessionId]) {
            metadataToKeep[sessionId] = metadata[sessionId];
          }
        } else {
          removedCount++;
        }
      });

      // Update storage
      localStorage.setItem(STORAGE_KEYS.SESSION_HISTORY, JSON.stringify(sessionsToKeep));
      localStorage.setItem(STORAGE_KEYS.SESSION_METADATA, JSON.stringify(metadataToKeep));

      const finalSize = this.calculateStorageSize();
      const freedSpace = initialSize - finalSize;

      // Update storage statistics
      this.updateStorageStats();

      this.logger('Automatic cleanup completed', { removedCount, freedSpace });

      return {
        success: true,
        data: { removedSessions: removedCount, freedSpace },
        timestamp
      };

    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown cleanup error';
      this.logger('Failed to perform automatic cleanup', error);
      
      return {
        success: false,
        error: errorMessage,
        timestamp
      };
    }
  }

  /**
   * Get storage statistics
   */
  getStorageStats(): StorageResult<StorageStats> {
    const timestamp = new Date().toISOString();
    
    try {
      const historyResult = this.getSessionHistory();
      const metadataResult = this.getAllSessionMetadata();

      if (!historyResult.success || !metadataResult.success) {
        return {
          success: false,
          error: 'Failed to load session data for statistics',
          timestamp
        };
      }

      const history = historyResult.data || {};
      const metadata = metadataResult.data || [];

      const activeSessions = metadata.filter(m => m.status === 'active').length;
      const finalizedSessions = metadata.filter(m => m.status === 'finalized').length;
      const totalMessages = Object.values(history).reduce((sum, session) => sum + session.messages.length, 0);

      const stats: StorageStats = {
        totalSessions: Object.keys(history).length,
        activeSessions,
        finalizedSessions,
        totalMessages,
        storageSize: this.calculateStorageSize(),
        lastCleanup: this.getLastCleanupTime(),
        lastAccess: timestamp
      };

      // Store stats for reference
      localStorage.setItem(STORAGE_KEYS.STORAGE_STATS, JSON.stringify(stats));

      return {
        success: true,
        data: stats,
        timestamp
      };

    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown error calculating stats';
      this.logger('Failed to calculate storage statistics', error);
      
      return {
        success: false,
        error: errorMessage,
        timestamp
      };
    }
  }

  /**
   * Update storage statistics
   */
  private updateStorageStats(): void {
    try {
      const statsResult = this.getStorageStats();
      if (statsResult.success && statsResult.data) {
        const updatedStats = {
          ...statsResult.data,
          lastAccess: new Date().toISOString()
        };
        localStorage.setItem(STORAGE_KEYS.STORAGE_STATS, JSON.stringify(updatedStats));
      }
    } catch (error) {
      this.logger('Failed to update storage statistics', error);
    }
  }

  /**
   * Calculate total storage size used by session data
   */
  private calculateStorageSize(): number {
    try {
      let totalSize = 0;
      
      Object.values(STORAGE_KEYS).forEach(key => {
        const item = localStorage.getItem(key);
        if (item) {
          totalSize += item.length;
        }
      });

      return totalSize;
    } catch (error) {
      this.logger('Failed to calculate storage size', error);
      return 0;
    }
  }

  /**
   * Get last cleanup time from storage stats
   */
  private getLastCleanupTime(): string {
    try {
      const statsJson = localStorage.getItem(STORAGE_KEYS.STORAGE_STATS);
      if (statsJson) {
        const stats = JSON.parse(statsJson);
        return stats.lastCleanup || 'Never';
      }
    } catch (error) {
      this.logger('Failed to get last cleanup time', error);
    }
    return 'Never';
  }

  /**
   * Validate session data structure
   */
  private validateSessionData(session: any): session is StoredSession {
    return session &&
           typeof session.sessionId === 'string' &&
           typeof session.userId === 'string' &&
           typeof session.createdAt === 'string' &&
           typeof session.lastActivity === 'string' &&
           typeof session.messageCount === 'number' &&
           (session.status === 'active' || session.status === 'finalized') &&
           Array.isArray(session.messages);
  }

  /**
   * Validate message data structure
   */
  private validateMessageData(message: any): message is StoredMessage {
    return message &&
           typeof message.id === 'string' &&
           typeof message.sessionId === 'string' &&
           (message.type === 'user' || message.type === 'assistant') &&
           typeof message.content === 'string' &&
           typeof message.timestamp === 'string' &&
           typeof message.sequence === 'number';
  }

  /**
   * Clear all session data (use with caution)
   */
  clearAllSessions(): StorageResult<void> {
    const timestamp = new Date().toISOString();
    
    try {
      Object.values(STORAGE_KEYS).forEach(key => {
        localStorage.removeItem(key);
      });

      this.logger('All session data cleared');
      
      return {
        success: true,
        timestamp
      };

    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown error clearing data';
      this.logger('Failed to clear session data', error);
      
      return {
        success: false,
        error: errorMessage,
        timestamp
      };
    }
  }
}

// Export singleton instance
export const sessionStorage = new SessionStorage();